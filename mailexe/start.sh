#!/bin/bash

# 临时邮箱服务启动脚本
# 适用于Ubuntu Server 22.04 LTS

# 设置变量
APP_NAME="mailplugin-server"
APP_VERSION="1.0.0"
JAR_FILE="mailexe-${APP_VERSION}.jar"
PID_FILE="/var/run/${APP_NAME}.pid"
LOG_DIR="/var/log/mailplugin"
LOG_FILE="${LOG_DIR}/mailplugin.log"
JAVA_OPTS="-Xms256m -Xmx512m -XX:+UseG1GC -XX:+UseStringDeduplication"
SPRING_PROFILES="production"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Java环境
check_java() {
    if ! command -v java &> /dev/null; then
        log_error "Java未安装，请先安装Java 11或更高版本"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | cut -d'.' -f1)
    if [ "$JAVA_VERSION" -lt 11 ]; then
        log_error "Java版本过低，需要Java 11或更高版本"
        exit 1
    fi
    
    log_info "Java版本检查通过: $(java -version 2>&1 | head -n 1)"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    # 创建日志目录
    if [ ! -d "$LOG_DIR" ]; then
        sudo mkdir -p "$LOG_DIR"
        sudo chown $USER:$USER "$LOG_DIR"
        log_info "创建日志目录: $LOG_DIR"
    fi
    
    # 创建PID文件目录
    PID_DIR=$(dirname "$PID_FILE")
    if [ ! -d "$PID_DIR" ]; then
        sudo mkdir -p "$PID_DIR"
        sudo chown $USER:$USER "$PID_DIR"
        log_info "创建PID目录: $PID_DIR"
    fi
}

# 检查应用是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 启动应用
start_app() {
    log_info "启动 $APP_NAME..."
    
    if is_running; then
        log_warn "应用已在运行中 (PID: $(cat $PID_FILE))"
        return 1
    fi
    
    # 检查JAR文件是否存在
    if [ ! -f "target/$JAR_FILE" ]; then
        log_error "JAR文件不存在: target/$JAR_FILE"
        log_info "请先运行: mvn clean package"
        exit 1
    fi
    
    # 启动应用
    nohup java $JAVA_OPTS \
        -Dspring.profiles.active=$SPRING_PROFILES \
        -Dlogging.file.name=$LOG_FILE \
        -jar "target/$JAR_FILE" \
        > "$LOG_DIR/startup.log" 2>&1 &
    
    PID=$!
    echo $PID > "$PID_FILE"
    
    # 等待启动
    log_info "等待应用启动..."
    sleep 5
    
    if is_running; then
        log_info "应用启动成功 (PID: $PID)"
        log_info "日志文件: $LOG_FILE"
        log_info "健康检查: curl http://localhost:8080/api/health"
        return 0
    else
        log_error "应用启动失败"
        cat "$LOG_DIR/startup.log"
        return 1
    fi
}

# 停止应用
stop_app() {
    log_info "停止 $APP_NAME..."
    
    if ! is_running; then
        log_warn "应用未运行"
        return 1
    fi
    
    PID=$(cat "$PID_FILE")
    log_info "发送停止信号到进程 $PID"
    
    # 优雅停止
    kill -TERM "$PID"
    
    # 等待进程结束
    for i in {1..30}; do
        if ! ps -p "$PID" > /dev/null 2>&1; then
            log_info "应用已停止"
            rm -f "$PID_FILE"
            return 0
        fi
        sleep 1
    done
    
    # 强制停止
    log_warn "优雅停止超时，强制停止进程"
    kill -KILL "$PID"
    rm -f "$PID_FILE"
    log_info "应用已强制停止"
}

# 重启应用
restart_app() {
    log_info "重启 $APP_NAME..."
    stop_app
    sleep 2
    start_app
}

# 查看状态
status_app() {
    if is_running; then
        PID=$(cat "$PID_FILE")
        log_info "应用正在运行 (PID: $PID)"
        
        # 显示内存使用情况
        if command -v ps &> /dev/null; then
            MEMORY=$(ps -p "$PID" -o rss= | awk '{print $1/1024 " MB"}')
            log_info "内存使用: $MEMORY"
        fi
        
        # 检查端口
        if command -v netstat &> /dev/null; then
            PORT_STATUS=$(netstat -tlnp 2>/dev/null | grep ":8080 " | grep "$PID")
            if [ -n "$PORT_STATUS" ]; then
                log_info "端口8080已监听"
            else
                log_warn "端口8080未监听"
            fi
        fi
    else
        log_warn "应用未运行"
    fi
}

# 查看日志
view_logs() {
    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        log_error "日志文件不存在: $LOG_FILE"
    fi
}

# 主函数
main() {
    case "$1" in
        start)
            check_java
            create_directories
            start_app
            ;;
        stop)
            stop_app
            ;;
        restart)
            check_java
            create_directories
            restart_app
            ;;
        status)
            status_app
            ;;
        logs)
            view_logs
            ;;
        *)
            echo "使用方法: $0 {start|stop|restart|status|logs}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动应用"
            echo "  stop    - 停止应用"
            echo "  restart - 重启应用"
            echo "  status  - 查看状态"
            echo "  logs    - 查看日志"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
