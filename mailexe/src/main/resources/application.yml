# 临时邮箱服务配置文件
# 生产环境配置

server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    threads:
      max: 50
      min-spare: 10
    connection-timeout: 20000
    max-connections: 200
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain

spring:
  application:
    name: mailplugin-server
  
  # 邮件配置
  mail:
    host: smtp.126.com
    port: 465
    username: da<PERSON><PERSON><EMAIL>
    password: TQVTv7gUbCGy9eBR
    protocol: smtp
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
            protocols: TLSv1.2
          starttls:
            enable: true
            required: true
        debug: false

  # Jackson JSON配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      indent-output: true

  # 异步配置
  task:
    execution:
      pool:
        core-size: 5
        max-size: 20
        queue-capacity: 100
        keep-alive: 60s
      thread-name-prefix: async-task-

# 自定义邮件配置
mail:
  pop3:
    host: pop.126.com
    port: 995
    ssl:
      enable: true
  username: dafs<PERSON><EMAIL>
  password: TQVTv7gUbCGy9eBR
  connection:
    timeout: 10000
  read:
    timeout: 10000

# CORS配置
cors:
  allowed:
    origins: "*"
    methods: "GET,POST,PUT,DELETE,OPTIONS"
    headers: "*"
  max:
    age: 3600

# 日志配置
logging:
  level:
    root: INFO
    com.mailplugin: INFO
    org.springframework.mail: WARN
    javax.mail: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/mailplugin.log
    max-size: 10MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  info:
    env:
      enabled: true

# 应用信息
info:
  app:
    name: 临时邮箱服务
    description: 提供一次性临时邮箱服务，支持邮件接收和验证码提取
    version: 1.0.0
    encoding: UTF-8
    java:
      version: 11

---
# 生产环境特定配置
spring:
  profiles: production
  
server:
  port: 8080
  
logging:
  level:
    root: WARN
    com.mailplugin: INFO
  file:
    name: /var/log/mailplugin/mailplugin.log

# 生产环境安全配置
security:
  api:
    key: mailplugin_api_key_2024
  rate:
    limit:
      enabled: true
      requests-per-minute: 60
      requests-per-hour: 300

---
# 开发环境配置
spring:
  profiles: development

server:
  port: 8080

logging:
  level:
    root: DEBUG
    com.mailplugin: DEBUG
    org.springframework.mail: DEBUG

# 开发环境邮件调试
mail:
  debug: true

---
# 测试环境配置
spring:
  profiles: test

server:
  port: 8081

logging:
  level:
    root: INFO
    com.mailplugin: DEBUG
