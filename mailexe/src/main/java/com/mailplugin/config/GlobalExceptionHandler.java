package com.mailplugin.config;

import com.mailplugin.model.EmailResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.WebRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.mail.MessagingException;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 
 * 功能说明：
 * - 统一处理应用程序异常
 * - 提供友好的错误响应
 * - 记录详细的错误日志
 * - 防止敏感信息泄露
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理邮件相关异常
     */
    @ExceptionHandler(MessagingException.class)
    @ResponseBody
    public ResponseEntity<EmailResponse> handleMessagingException(MessagingException ex, WebRequest request) {
        logger.error("邮件服务异常: {}", ex.getMessage(), ex);
        
        String userMessage = "邮件服务暂时不可用，请稍后重试";
        if (ex.getMessage().contains("authentication")) {
            userMessage = "邮件服务认证失败";
        } else if (ex.getMessage().contains("connection")) {
            userMessage = "邮件服务连接失败";
        }
        
        EmailResponse response = new EmailResponse(userMessage);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseBody
    public ResponseEntity<EmailResponse> handleIllegalArgumentException(IllegalArgumentException ex, WebRequest request) {
        logger.warn("非法参数异常: {}", ex.getMessage());
        
        EmailResponse response = new EmailResponse("请求参数无效: " + ex.getMessage());
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseBody
    public ResponseEntity<EmailResponse> handleNullPointerException(NullPointerException ex, WebRequest request) {
        logger.error("空指针异常: {}", ex.getMessage(), ex);
        
        EmailResponse response = new EmailResponse("服务器内部错误，请联系管理员");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseBody
    public ResponseEntity<EmailResponse> handleRuntimeException(RuntimeException ex, WebRequest request) {
        logger.error("运行时异常: {}", ex.getMessage(), ex);
        
        String userMessage = "服务器处理请求时发生错误";
        
        // 根据异常类型提供更具体的错误信息
        if (ex.getMessage().contains("timeout")) {
            userMessage = "请求超时，请稍后重试";
        } else if (ex.getMessage().contains("connection")) {
            userMessage = "网络连接异常，请检查网络状态";
        }
        
        EmailResponse response = new EmailResponse(userMessage);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleGenericException(Exception ex, WebRequest request) {
        logger.error("未处理的异常: {}", ex.getMessage(), ex);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "服务器内部错误，请稍后重试");
        response.put("timestamp", java.time.LocalDateTime.now().toString());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理安全相关异常
     */
    @ExceptionHandler(SecurityException.class)
    @ResponseBody
    public ResponseEntity<EmailResponse> handleSecurityException(SecurityException ex, WebRequest request) {
        logger.warn("安全异常: {}", ex.getMessage());
        
        EmailResponse response = new EmailResponse("访问被拒绝，请检查权限");
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }

    /**
     * 处理IO异常
     */
    @ExceptionHandler(java.io.IOException.class)
    @ResponseBody
    public ResponseEntity<EmailResponse> handleIOException(java.io.IOException ex, WebRequest request) {
        logger.error("IO异常: {}", ex.getMessage(), ex);
        
        EmailResponse response = new EmailResponse("文件操作失败，请稍后重试");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
