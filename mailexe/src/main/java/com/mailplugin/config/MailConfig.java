package com.mailplugin.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.mail.Session;
import java.util.Properties;
import java.util.concurrent.Executor;

/**
 * 邮件服务配置类
 * 
 * 功能说明：
 * - 配置POP3连接参数
 * - 配置邮件会话
 * - 配置异步任务执行器
 * - 管理邮件服务相关的安全配置
 */
@Configuration
public class MailConfig {

    private static final Logger logger = LoggerFactory.getLogger(MailConfig.class);

    // POP3服务器配置
    @Value("${mail.pop3.host:pop.126.com}")
    private String pop3Host;

    @Value("${mail.pop3.port:995}")
    private int pop3Port;

    @Value("${mail.pop3.ssl.enable:true}")
    private boolean sslEnable;

    // 中转邮箱配置
    @Value("${mail.username:<EMAIL>}")
    private String username;

    @Value("${mail.password:TQVTv7gUbCGy9eBR}")
    private String password;

    // 连接超时配置
    @Value("${mail.connection.timeout:10000}")
    private int connectionTimeout;

    @Value("${mail.read.timeout:10000}")
    private int readTimeout;

    /**
     * 配置POP3邮件会话
     */
    @Bean
    public Session mailSession() {
        Properties props = new Properties();
        
        // POP3服务器配置
        props.setProperty("mail.store.protocol", "pop3");
        props.setProperty("mail.pop3.host", pop3Host);
        props.setProperty("mail.pop3.port", String.valueOf(pop3Port));
        
        // SSL/TLS配置
        if (sslEnable) {
            props.setProperty("mail.pop3.ssl.enable", "true");
            props.setProperty("mail.pop3.ssl.protocols", "TLSv1.2");
            props.setProperty("mail.pop3.ssl.trust", "*");
        }
        
        // 连接超时配置
        props.setProperty("mail.pop3.connectiontimeout", String.valueOf(connectionTimeout));
        props.setProperty("mail.pop3.timeout", String.valueOf(readTimeout));
        
        // 性能优化配置
        props.setProperty("mail.pop3.rsetbeforequit", "true");
        props.setProperty("mail.pop3.disabletop", "true");
        
        Session session = Session.getInstance(props);
        
        logger.info("邮件会话配置完成 - 服务器: {}:{}, SSL: {}", pop3Host, pop3Port, sslEnable);
        return session;
    }

    /**
     * 配置异步任务执行器
     * 用于处理邮件获取的并发请求
     */
    @Bean(name = "mailTaskExecutor")
    public Executor mailTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(5);
        // 最大线程数
        executor.setMaxPoolSize(20);
        // 队列容量
        executor.setQueueCapacity(100);
        // 线程名前缀
        executor.setThreadNamePrefix("MailTask-");
        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        // 等待任务完成后关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        logger.info("邮件任务执行器配置完成 - 核心线程: {}, 最大线程: {}", 5, 20);
        return executor;
    }

    // Getter方法供其他组件使用
    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public String getPop3Host() {
        return pop3Host;
    }

    public int getPop3Port() {
        return pop3Port;
    }

    public boolean isSslEnable() {
        return sslEnable;
    }

    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }
}
