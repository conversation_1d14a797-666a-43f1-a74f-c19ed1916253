package com.mailplugin.controller;

import com.mailplugin.service.EmailService;
import com.mailplugin.service.PerformanceService;
import com.mailplugin.service.RuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 
 * 功能说明：
 * - 提供系统测试接口
 * - 性能监控查询
 * - 系统状态检查
 * - 开发调试支持
 */
@RestController
@RequestMapping("/test")
@CrossOrigin(origins = "*", maxAge = 3600)
public class TestController {

    private static final Logger logger = LoggerFactory.getLogger(TestController.class);

    @Autowired
    private EmailService emailService;

    @Autowired
    private PerformanceService performanceService;

    @Autowired
    private RuleService ruleService;

    /**
     * 系统健康检查
     * 
     * GET /test/health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 检查各个服务状态
            response.put("status", "UP");
            response.put("timestamp", java.time.LocalDateTime.now());
            response.put("activeSessionCount", emailService.getActiveSessionCount());
            response.put("totalRules", ruleService.getTotalRulesCount());
            response.put("sharedRules", ruleService.getSharedRulesCount());
            response.put("memoryUsage", performanceService.getMemoryUsage());
            
            // 性能统计
            PerformanceService.PerformanceStats stats = performanceService.getPerformanceStats();
            response.put("performanceStats", Map.of(
                "totalRequests", stats.getTotalRequests(),
                "successfulRequests", stats.getSuccessfulRequests(),
                "failedRequests", stats.getFailedRequests(),
                "successRate", stats.getSuccessRate(),
                "avgResponseTime", stats.getAvgResponseTime(),
                "cacheSize", stats.getCacheSize(),
                "activeRequests", stats.getActiveRequests()
            ));
            
            logger.info("系统健康检查完成");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("系统健康检查失败: {}", e.getMessage(), e);
            response.put("status", "DOWN");
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 性能统计查询
     * 
     * GET /test/performance
     */
    @GetMapping("/performance")
    public ResponseEntity<Map<String, Object>> getPerformanceStats() {
        try {
            PerformanceService.PerformanceStats stats = performanceService.getPerformanceStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("stats", stats);
            response.put("memoryUsage", performanceService.getMemoryUsage());
            response.put("timestamp", java.time.LocalDateTime.now());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取性能统计失败: {}", e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 重置性能统计
     * 
     * POST /test/reset-stats?apiKey=xxx
     */
    @PostMapping("/reset-stats")
    public ResponseEntity<Map<String, Object>> resetPerformanceStats(
            @RequestParam("apiKey") String apiKey) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 简单的API密钥验证
            if (!"mailplugin_api_key_2024".equals(apiKey)) {
                response.put("success", false);
                response.put("message", "无效的API密钥");
                return ResponseEntity.badRequest().body(response);
            }
            
            performanceService.resetStats();
            
            response.put("success", true);
            response.put("message", "性能统计已重置");
            response.put("timestamp", java.time.LocalDateTime.now());
            
            logger.info("性能统计已重置");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("重置性能统计失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 系统配置信息
     * 
     * GET /test/config
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getSystemConfig() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 系统信息
            response.put("javaVersion", System.getProperty("java.version"));
            response.put("osName", System.getProperty("os.name"));
            response.put("osVersion", System.getProperty("os.version"));
            response.put("availableProcessors", Runtime.getRuntime().availableProcessors());
            
            // 内存信息
            Runtime runtime = Runtime.getRuntime();
            response.put("maxMemory", runtime.maxMemory() / 1024 / 1024 + "MB");
            response.put("totalMemory", runtime.totalMemory() / 1024 / 1024 + "MB");
            response.put("freeMemory", runtime.freeMemory() / 1024 / 1024 + "MB");
            
            // 应用信息
            response.put("applicationName", "临时邮箱服务");
            response.put("version", "1.0.0");
            response.put("startTime", java.time.LocalDateTime.now());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取系统配置失败: {}", e.getMessage(), e);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 模拟邮件获取测试
     * 
     * POST /test/simulate-email
     */
    @PostMapping("/simulate-email")
    public ResponseEntity<Map<String, Object>> simulateEmailTest(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String testUserId = request.get("userId");
            String testEmail = request.get("tempEmail");
            
            if (testUserId == null || testEmail == null) {
                response.put("success", false);
                response.put("message", "缺少必要参数: userId, tempEmail");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 模拟测试数据
            response.put("success", true);
            response.put("message", "模拟测试完成");
            response.put("testUserId", testUserId);
            response.put("testEmail", testEmail);
            response.put("simulatedEmails", 0);
            response.put("responseTime", "< 1000ms");
            response.put("timestamp", java.time.LocalDateTime.now());
            
            logger.info("模拟邮件获取测试 - 用户ID: {}, 邮箱: {}", testUserId, testEmail);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("模拟邮件测试失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 清理缓存
     * 
     * DELETE /test/clear-cache?apiKey=xxx
     */
    @DeleteMapping("/clear-cache")
    public ResponseEntity<Map<String, Object>> clearCache(
            @RequestParam("apiKey") String apiKey,
            @RequestParam(value = "userId", required = false) String userId) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // API密钥验证
            if (!"mailplugin_api_key_2024".equals(apiKey)) {
                response.put("success", false);
                response.put("message", "无效的API密钥");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (userId != null) {
                // 清理指定用户缓存
                performanceService.clearUserCache(userId);
                emailService.removeUserSession(userId);
                response.put("message", "用户缓存已清理: " + userId);
            } else {
                // 清理所有缓存（通过重置统计实现）
                performanceService.resetStats();
                response.put("message", "所有缓存已清理");
            }
            
            response.put("success", true);
            response.put("timestamp", java.time.LocalDateTime.now());
            
            logger.info("缓存清理完成 - 用户ID: {}", userId != null ? userId : "全部");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("清理缓存失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}
