package com.mailplugin.controller;

import com.mailplugin.model.ParseRule;
import com.mailplugin.service.RuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 解析规则API控制器
 * 
 * 功能说明：
 * - 管理共享解析规则
 * - 提供规则的增删改查接口
 * - 支持按发件人查询规则
 */
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*", maxAge = 3600)
public class RuleController {

    private static final Logger logger = LoggerFactory.getLogger(RuleController.class);

    @Autowired
    private RuleService ruleService;

    /**
     * 获取所有共享解析规则
     * 
     * GET /api/get-shared-rules
     * 
     * @return 共享规则列表
     */
    @GetMapping("/get-shared-rules")
    public ResponseEntity<Map<String, Object>> getSharedRules() {
        try {
            logger.info("收到获取共享解析规则请求");

            List<ParseRule> rules = ruleService.getAllSharedRules();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取共享规则成功");
            response.put("rules", rules);
            response.put("count", rules.size());

            logger.info("返回共享解析规则，数量: {}", rules.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取共享解析规则时发生错误: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取共享规则失败: " + e.getMessage());
            errorResponse.put("rules", null);
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 根据发件人邮箱获取解析规则
     * 
     * GET /api/get-rules-by-sender?senderEmail=xxx
     * 
     * @param senderEmail 发件人邮箱
     * @return 匹配的规则列表
     */
    @GetMapping("/get-rules-by-sender")
    public ResponseEntity<Map<String, Object>> getRulesBySender(
            @RequestParam("senderEmail") String senderEmail) {
        
        try {
            logger.info("收到根据发件人获取规则请求: {}", senderEmail);

            List<ParseRule> rules = ruleService.getRulesBySender(senderEmail);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取规则成功");
            response.put("rules", rules);
            response.put("count", rules.size());
            response.put("senderEmail", senderEmail);

            logger.info("返回发件人 {} 的解析规则，数量: {}", senderEmail, rules.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("根据发件人获取规则时发生错误: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取规则失败: " + e.getMessage());
            errorResponse.put("rules", null);
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 上传共享解析规则
     * 
     * POST /api/upload-shared-rule
     * 
     * @param request 包含规则和API密钥的请求
     * @return 处理结果
     */
    @PostMapping("/upload-shared-rule")
    public ResponseEntity<Map<String, Object>> uploadSharedRule(@RequestBody Map<String, Object> request) {
        try {
            logger.info("收到上传共享解析规则请求");

            // 提取API密钥
            String apiKey = (String) request.get("apiKey");
            if (apiKey == null || apiKey.trim().isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "缺少API密钥");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 提取规则数据
            @SuppressWarnings("unchecked")
            Map<String, Object> ruleData = (Map<String, Object>) request.get("rule");
            if (ruleData == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "缺少规则数据");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 构建ParseRule对象
            ParseRule rule = new ParseRule();
            rule.setSenderEmail((String) ruleData.get("senderEmail"));
            rule.setPlatform((String) ruleData.get("platform"));
            rule.setPattern((String) ruleData.get("pattern"));
            rule.setPatternType((String) ruleData.get("patternType"));
            rule.setExample((String) ruleData.get("example"));
            rule.setDescription((String) ruleData.get("description"));
            rule.setShared(true); // 强制设置为共享
            rule.setCreatedBy((String) ruleData.get("createdBy"));

            // 添加规则
            boolean success = ruleService.addRule(rule, apiKey);

            Map<String, Object> response = new HashMap<>();
            if (success) {
                response.put("success", true);
                response.put("message", "共享规则上传成功");
                response.put("ruleId", rule.getId());
                
                logger.info("共享解析规则上传成功 - ID: {}, 发件人: {}", 
                        rule.getId(), rule.getSenderEmail());
                
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "共享规则上传失败");
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("上传共享解析规则时发生错误: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "上传共享规则失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 更新解析规则
     * 
     * PUT /api/update-rule
     * 
     * @param request 包含规则和API密钥的请求
     * @return 处理结果
     */
    @PutMapping("/update-rule")
    public ResponseEntity<Map<String, Object>> updateRule(@RequestBody Map<String, Object> request) {
        try {
            logger.info("收到更新解析规则请求");

            String apiKey = (String) request.get("apiKey");
            @SuppressWarnings("unchecked")
            Map<String, Object> ruleData = (Map<String, Object>) request.get("rule");

            if (apiKey == null || ruleData == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "缺少必要参数");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 构建ParseRule对象
            ParseRule rule = new ParseRule();
            rule.setId((String) ruleData.get("id"));
            rule.setSenderEmail((String) ruleData.get("senderEmail"));
            rule.setPlatform((String) ruleData.get("platform"));
            rule.setPattern((String) ruleData.get("pattern"));
            rule.setPatternType((String) ruleData.get("patternType"));
            rule.setExample((String) ruleData.get("example"));
            rule.setDescription((String) ruleData.get("description"));
            rule.setShared(true);

            boolean success = ruleService.updateRule(rule, apiKey);

            Map<String, Object> response = new HashMap<>();
            if (success) {
                response.put("success", true);
                response.put("message", "规则更新成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "规则更新失败");
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("更新解析规则时发生错误: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "更新规则失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 删除解析规则
     * 
     * DELETE /api/delete-rule/{ruleId}?apiKey=xxx
     * 
     * @param ruleId 规则ID
     * @param apiKey API密钥
     * @return 处理结果
     */
    @DeleteMapping("/delete-rule/{ruleId}")
    public ResponseEntity<Map<String, Object>> deleteRule(
            @PathVariable("ruleId") String ruleId,
            @RequestParam("apiKey") String apiKey) {
        
        try {
            logger.info("收到删除解析规则请求: {}", ruleId);

            boolean success = ruleService.deleteRule(ruleId, apiKey);

            Map<String, Object> response = new HashMap<>();
            if (success) {
                response.put("success", true);
                response.put("message", "规则删除成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "规则删除失败");
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("删除解析规则时发生错误: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "删除规则失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 获取规则统计信息
     * 
     * GET /api/rules-stats
     * 
     * @return 统计信息
     */
    @GetMapping("/rules-stats")
    public ResponseEntity<Map<String, Object>> getRulesStats() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("totalRules", ruleService.getTotalRulesCount());
            response.put("sharedRules", ruleService.getSharedRulesCount());
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取规则统计信息时发生错误: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取统计信息失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
}
