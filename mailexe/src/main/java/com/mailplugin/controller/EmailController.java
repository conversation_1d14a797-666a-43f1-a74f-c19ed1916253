package com.mailplugin.controller;

import com.mailplugin.model.EmailRequest;
import com.mailplugin.model.EmailResponse;
import com.mailplugin.service.EmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

/**
 * 邮件API控制器
 * 
 * 功能说明：
 * - 处理临时邮箱生成请求
 * - 处理邮件获取请求
 * - 提供系统状态查询
 * - 支持异步处理
 */
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*", maxAge = 3600)
public class EmailController {

    private static final Logger logger = LoggerFactory.getLogger(EmailController.class);

    @Autowired
    private EmailService emailService;

    /**
     * 生成临时邮箱
     * 
     * POST /api/generate-email
     * 
     * @param request 邮件请求
     * @return 处理结果
     */
    @PostMapping("/generate-email")
    public ResponseEntity<EmailResponse> generateEmail(@RequestBody EmailRequest request) {
        try {
            logger.info("收到临时邮箱生成请求: 用户ID={}, 邮箱={}", 
                    request.getUserId(), request.getTempEmail());

            EmailResponse response = emailService.generateTempEmail(request);

            if (response.isSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("处理临时邮箱生成请求时发生错误: {}", e.getMessage(), e);
            EmailResponse errorResponse = new EmailResponse("服务器内部错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 获取邮件数据
     * 
     * GET /api/get-emails?userId=xxx&apiKey=xxx
     * 
     * @param userId 用户ID
     * @param apiKey API密钥
     * @return 邮件数据
     */
    @GetMapping("/get-emails")
    public ResponseEntity<EmailResponse> getEmails(
            @RequestParam("userId") String userId,
            @RequestParam("apiKey") String apiKey) {
        
        try {
            logger.info("收到邮件获取请求: 用户ID={}", userId);

            EmailResponse response = emailService.getUserEmails(userId, apiKey);

            if (response.isSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("处理邮件获取请求时发生错误: {}", e.getMessage(), e);
            EmailResponse errorResponse = new EmailResponse("服务器内部错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 异步获取邮件数据
     * 
     * GET /api/get-emails-async?userId=xxx&apiKey=xxx
     * 
     * @param userId 用户ID
     * @param apiKey API密钥
     * @return 异步邮件数据
     */
    @GetMapping("/get-emails-async")
    public CompletableFuture<ResponseEntity<EmailResponse>> getEmailsAsync(
            @RequestParam("userId") String userId,
            @RequestParam("apiKey") String apiKey) {
        
        try {
            logger.info("收到异步邮件获取请求: 用户ID={}", userId);

            return emailService.getUserEmailsAsync(userId, apiKey)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            return ResponseEntity.ok(response);
                        } else {
                            return ResponseEntity.badRequest().body(response);
                        }
                    })
                    .exceptionally(throwable -> {
                        logger.error("异步处理邮件获取请求时发生错误: {}", throwable.getMessage(), throwable);
                        EmailResponse errorResponse = new EmailResponse("服务器内部错误: " + throwable.getMessage());
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
                    });

        } catch (Exception e) {
            logger.error("启动异步邮件获取时发生错误: {}", e.getMessage(), e);
            EmailResponse errorResponse = new EmailResponse("服务器内部错误: " + e.getMessage());
            return CompletableFuture.completedFuture(
                    ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse));
        }
    }

    /**
     * 清理用户会话
     * 
     * DELETE /api/session/{userId}?apiKey=xxx
     * 
     * @param userId 用户ID
     * @param apiKey API密钥
     * @return 处理结果
     */
    @DeleteMapping("/session/{userId}")
    public ResponseEntity<EmailResponse> clearUserSession(
            @PathVariable("userId") String userId,
            @RequestParam("apiKey") String apiKey) {
        
        try {
            logger.info("收到清理用户会话请求: 用户ID={}", userId);

            // 简单的API密钥验证
            if (!"mailplugin_api_key_2024".equals(apiKey)) {
                EmailResponse errorResponse = new EmailResponse("无效的API密钥");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            emailService.removeUserSession(userId);
            EmailResponse response = new EmailResponse("用户会话清理成功");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("清理用户会话时发生错误: {}", e.getMessage(), e);
            EmailResponse errorResponse = new EmailResponse("服务器内部错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 获取系统状态
     * 
     * GET /api/status
     * 
     * @return 系统状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<EmailResponse> getSystemStatus() {
        try {
            int activeSessionCount = emailService.getActiveSessionCount();
            
            EmailResponse response = new EmailResponse("系统运行正常");
            response.setMessage(String.format("系统运行正常，当前活跃会话数: %d", activeSessionCount));
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取系统状态时发生错误: {}", e.getMessage(), e);
            EmailResponse errorResponse = new EmailResponse("系统状态检查失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 健康检查接口
     * 
     * GET /api/health
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("OK");
    }
}
