package com.mailplugin.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;

/**
 * 解析规则模型
 * 
 * 用于存储和管理邮件内容解析规则
 */
public class ParseRule {

    @JsonProperty("id")
    private String id;

    @JsonProperty("senderEmail")
    private String senderEmail;

    @JsonProperty("platform")
    private String platform;

    @JsonProperty("pattern")
    private String pattern;

    @JsonProperty("patternType")
    private String patternType; // "CODE" 或 "LINK"

    @JsonProperty("example")
    private String example;

    @JsonProperty("description")
    private String description;

    @JsonProperty("createdTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    @JsonProperty("isShared")
    private boolean isShared;

    @JsonProperty("createdBy")
    private String createdBy;

    // 默认构造函数
    public ParseRule() {
        this.createdTime = LocalDateTime.now();
    }

    // 全参构造函数
    public ParseRule(String id, String senderEmail, String platform, String pattern, 
                     String patternType, String example, String description, 
                     boolean isShared, String createdBy) {
        this();
        this.id = id;
        this.senderEmail = senderEmail;
        this.platform = platform;
        this.pattern = pattern;
        this.patternType = patternType;
        this.example = example;
        this.description = description;
        this.isShared = isShared;
        this.createdBy = createdBy;
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSenderEmail() {
        return senderEmail;
    }

    public void setSenderEmail(String senderEmail) {
        this.senderEmail = senderEmail;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }

    public String getPatternType() {
        return patternType;
    }

    public void setPatternType(String patternType) {
        this.patternType = patternType;
    }

    public String getExample() {
        return example;
    }

    public void setExample(String example) {
        this.example = example;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public boolean isShared() {
        return isShared;
    }

    public void setShared(boolean shared) {
        isShared = shared;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public String toString() {
        return "ParseRule{" +
                "id='" + id + '\'' +
                ", senderEmail='" + senderEmail + '\'' +
                ", platform='" + platform + '\'' +
                ", patternType='" + patternType + '\'' +
                ", isShared=" + isShared +
                ", createdTime=" + createdTime +
                '}';
    }
}
