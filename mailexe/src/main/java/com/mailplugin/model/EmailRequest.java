package com.mailplugin.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;

/**
 * 邮件请求模型
 * 
 * 用于接收插件发送的临时邮箱生成请求
 */
public class EmailRequest {

    @JsonProperty("userId")
    private String userId;

    @JsonProperty("tempEmail")
    private String tempEmail;

    @JsonProperty("generateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime generateTime;

    @JsonProperty("apiKey")
    private String apiKey;

    // 默认构造函数
    public EmailRequest() {}

    // 全参构造函数
    public EmailRequest(String userId, String tempEmail, LocalDateTime generateTime, String apiKey) {
        this.userId = userId;
        this.tempEmail = tempEmail;
        this.generateTime = generateTime;
        this.apiKey = apiKey;
    }

    // Getter和Setter方法
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTempEmail() {
        return tempEmail;
    }

    public void setTempEmail(String tempEmail) {
        this.tempEmail = tempEmail;
    }

    public LocalDateTime getGenerateTime() {
        return generateTime;
    }

    public void setGenerateTime(LocalDateTime generateTime) {
        this.generateTime = generateTime;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    @Override
    public String toString() {
        return "EmailRequest{" +
                "userId='" + userId + '\'' +
                ", tempEmail='" + tempEmail + '\'' +
                ", generateTime=" + generateTime +
                ", apiKey='[PROTECTED]'" +
                '}';
    }
}
