package com.mailplugin.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 邮件响应模型
 * 
 * 用于返回邮件数据给插件
 */
public class EmailResponse {

    @JsonProperty("success")
    private boolean success;

    @JsonProperty("message")
    private String message;

    @JsonProperty("emails")
    private List<EmailData> emails;

    @JsonProperty("timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;

    // 默认构造函数
    public EmailResponse() {
        this.timestamp = LocalDateTime.now();
    }

    // 成功响应构造函数
    public EmailResponse(List<EmailData> emails) {
        this();
        this.success = true;
        this.message = "邮件获取成功";
        this.emails = emails;
    }

    // 错误响应构造函数
    public EmailResponse(String errorMessage) {
        this();
        this.success = false;
        this.message = errorMessage;
        this.emails = null;
    }

    // 成功响应构造函数（带消息）
    public EmailResponse(String successMessage, boolean isSuccess) {
        this();
        this.success = isSuccess;
        this.message = successMessage;
        this.emails = null;
    }

    // 内部邮件数据类
    public static class EmailData {
        @JsonProperty("from")
        private String from;

        @JsonProperty("subject")
        private String subject;

        @JsonProperty("content")
        private String content;

        @JsonProperty("receivedTime")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime receivedTime;

        @JsonProperty("to")
        private String to;

        // 默认构造函数
        public EmailData() {}

        // 全参构造函数
        public EmailData(String from, String subject, String content, LocalDateTime receivedTime, String to) {
            this.from = from;
            this.subject = subject;
            this.content = content;
            this.receivedTime = receivedTime;
            this.to = to;
        }

        // Getter和Setter方法
        public String getFrom() {
            return from;
        }

        public void setFrom(String from) {
            this.from = from;
        }

        public String getSubject() {
            return subject;
        }

        public void setSubject(String subject) {
            this.subject = subject;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public LocalDateTime getReceivedTime() {
            return receivedTime;
        }

        public void setReceivedTime(LocalDateTime receivedTime) {
            this.receivedTime = receivedTime;
        }

        public String getTo() {
            return to;
        }

        public void setTo(String to) {
            this.to = to;
        }

        @Override
        public String toString() {
            return "EmailData{" +
                    "from='" + from + '\'' +
                    ", subject='" + subject + '\'' +
                    ", receivedTime=" + receivedTime +
                    ", to='" + to + '\'' +
                    '}';
        }
    }

    // Getter和Setter方法
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<EmailData> getEmails() {
        return emails;
    }

    public void setEmails(List<EmailData> emails) {
        this.emails = emails;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "EmailResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", emailCount=" + (emails != null ? emails.size() : 0) +
                ", timestamp=" + timestamp +
                '}';
    }
}
