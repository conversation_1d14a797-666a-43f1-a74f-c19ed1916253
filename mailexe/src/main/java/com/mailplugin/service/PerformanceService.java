package com.mailplugin.service;

import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能监控和优化服务
 * 
 * 功能说明：
 * - 监控系统性能指标
 * - 实现请求缓存机制
 * - 提供性能统计信息
 * - 自动清理过期数据
 */
@Service
public class PerformanceService {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceService.class);

    // 性能统计
    private final AtomicInteger totalRequests = new AtomicInteger(0);
    private final AtomicInteger successfulRequests = new AtomicInteger(0);
    private final AtomicInteger failedRequests = new AtomicInteger(0);
    private final AtomicLong totalResponseTime = new AtomicLong(0);

    // 邮件缓存（用户ID -> 邮件数据）
    private final ConcurrentHashMap<String, CachedEmailData> emailCache = new ConcurrentHashMap<>();

    // 请求时间记录
    private final ConcurrentHashMap<String, Long> requestStartTimes = new ConcurrentHashMap<>();

    /**
     * 缓存的邮件数据
     */
    private static class CachedEmailData {
        private final Object emailData;
        private final LocalDateTime cacheTime;
        private final LocalDateTime expireTime;

        public CachedEmailData(Object emailData, int cacheMinutes) {
            this.emailData = emailData;
            this.cacheTime = LocalDateTime.now();
            this.expireTime = this.cacheTime.plusMinutes(cacheMinutes);
        }

        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expireTime);
        }

        public Object getEmailData() {
            return emailData;
        }

        public LocalDateTime getCacheTime() {
            return cacheTime;
        }
    }

    @PostConstruct
    public void init() {
        logger.info("性能监控服务初始化完成");
    }

    /**
     * 记录请求开始时间
     * 
     * @param requestId 请求ID
     */
    public void recordRequestStart(String requestId) {
        requestStartTimes.put(requestId, System.currentTimeMillis());
        totalRequests.incrementAndGet();
    }

    /**
     * 记录请求完成时间
     * 
     * @param requestId 请求ID
     * @param success 是否成功
     */
    public void recordRequestEnd(String requestId, boolean success) {
        Long startTime = requestStartTimes.remove(requestId);
        if (startTime != null) {
            long responseTime = System.currentTimeMillis() - startTime;
            totalResponseTime.addAndGet(responseTime);
            
            if (success) {
                successfulRequests.incrementAndGet();
            } else {
                failedRequests.incrementAndGet();
            }
            
            // 记录慢请求
            if (responseTime > 5000) { // 超过5秒
                logger.warn("慢请求检测 - 请求ID: {}, 响应时间: {}ms", requestId, responseTime);
            }
        }
    }

    /**
     * 缓存邮件数据
     * 
     * @param userId 用户ID
     * @param emailData 邮件数据
     * @param cacheMinutes 缓存时间（分钟）
     */
    public void cacheEmailData(String userId, Object emailData, int cacheMinutes) {
        if (userId != null && emailData != null) {
            emailCache.put(userId, new CachedEmailData(emailData, cacheMinutes));
            logger.debug("缓存邮件数据 - 用户ID: {}, 缓存时间: {}分钟", userId, cacheMinutes);
        }
    }

    /**
     * 获取缓存的邮件数据
     * 
     * @param userId 用户ID
     * @return 缓存的邮件数据，如果不存在或已过期则返回null
     */
    public Object getCachedEmailData(String userId) {
        if (userId == null) {
            return null;
        }

        CachedEmailData cached = emailCache.get(userId);
        if (cached == null) {
            return null;
        }

        if (cached.isExpired()) {
            emailCache.remove(userId);
            logger.debug("移除过期缓存 - 用户ID: {}", userId);
            return null;
        }

        logger.debug("命中邮件缓存 - 用户ID: {}, 缓存时间: {}", userId, cached.getCacheTime());
        return cached.getEmailData();
    }

    /**
     * 清除用户缓存
     * 
     * @param userId 用户ID
     */
    public void clearUserCache(String userId) {
        if (userId != null) {
            emailCache.remove(userId);
            logger.debug("清除用户缓存 - 用户ID: {}", userId);
        }
    }

    /**
     * 获取性能统计信息
     * 
     * @return 性能统计
     */
    public PerformanceStats getPerformanceStats() {
        int total = totalRequests.get();
        int successful = successfulRequests.get();
        int failed = failedRequests.get();
        long totalTime = totalResponseTime.get();

        double successRate = total > 0 ? (double) successful / total * 100 : 0;
        double avgResponseTime = successful > 0 ? (double) totalTime / successful : 0;

        return new PerformanceStats(
                total,
                successful,
                failed,
                successRate,
                avgResponseTime,
                emailCache.size(),
                requestStartTimes.size()
        );
    }

    /**
     * 性能统计数据类
     */
    public static class PerformanceStats {
        private final int totalRequests;
        private final int successfulRequests;
        private final int failedRequests;
        private final double successRate;
        private final double avgResponseTime;
        private final int cacheSize;
        private final int activeRequests;

        public PerformanceStats(int totalRequests, int successfulRequests, int failedRequests,
                               double successRate, double avgResponseTime, int cacheSize, int activeRequests) {
            this.totalRequests = totalRequests;
            this.successfulRequests = successfulRequests;
            this.failedRequests = failedRequests;
            this.successRate = successRate;
            this.avgResponseTime = avgResponseTime;
            this.cacheSize = cacheSize;
            this.activeRequests = activeRequests;
        }

        // Getter方法
        public int getTotalRequests() { return totalRequests; }
        public int getSuccessfulRequests() { return successfulRequests; }
        public int getFailedRequests() { return failedRequests; }
        public double getSuccessRate() { return successRate; }
        public double getAvgResponseTime() { return avgResponseTime; }
        public int getCacheSize() { return cacheSize; }
        public int getActiveRequests() { return activeRequests; }

        @Override
        public String toString() {
            return String.format(
                "PerformanceStats{总请求=%d, 成功=%d, 失败=%d, 成功率=%.2f%%, 平均响应时间=%.2fms, 缓存大小=%d, 活跃请求=%d}",
                totalRequests, successfulRequests, failedRequests, successRate, avgResponseTime, cacheSize, activeRequests
            );
        }
    }

    /**
     * 定时清理过期缓存和统计数据
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    @Async("mailTaskExecutor")
    public void cleanupExpiredData() {
        try {
            // 清理过期邮件缓存
            int removedCacheCount = 0;
            for (String userId : emailCache.keySet()) {
                CachedEmailData cached = emailCache.get(userId);
                if (cached != null && cached.isExpired()) {
                    emailCache.remove(userId);
                    removedCacheCount++;
                }
            }

            // 清理超时的请求记录
            long currentTime = System.currentTimeMillis();
            int removedRequestCount = 0;
            for (String requestId : requestStartTimes.keySet()) {
                Long startTime = requestStartTimes.get(requestId);
                if (startTime != null && (currentTime - startTime) > 300000) { // 5分钟超时
                    requestStartTimes.remove(requestId);
                    removedRequestCount++;
                }
            }

            if (removedCacheCount > 0 || removedRequestCount > 0) {
                logger.info("定时清理完成 - 清理过期缓存: {}, 清理超时请求: {}", removedCacheCount, removedRequestCount);
            }

            // 记录性能统计
            PerformanceStats stats = getPerformanceStats();
            logger.info("性能统计: {}", stats);

        } catch (Exception e) {
            logger.error("定时清理任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 重置性能统计
     */
    public void resetStats() {
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        totalResponseTime.set(0);
        logger.info("性能统计已重置");
    }

    /**
     * 获取系统内存使用情况
     * 
     * @return 内存使用信息
     */
    public String getMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();

        return String.format(
            "内存使用: 已用=%dMB, 空闲=%dMB, 总计=%dMB, 最大=%dMB",
            usedMemory / 1024 / 1024,
            freeMemory / 1024 / 1024,
            totalMemory / 1024 / 1024,
            maxMemory / 1024 / 1024
        );
    }
}
