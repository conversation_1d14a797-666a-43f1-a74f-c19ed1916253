package com.mailplugin.service;

import com.mailplugin.config.MailConfig;
import com.mailplugin.model.EmailResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.mail.*;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * POP3邮件服务类
 * 
 * 功能说明：
 * - 连接126邮箱POP3服务器
 * - 获取指定时间后的邮件
 * - 过滤目标邮箱的邮件
 * - 解析邮件内容
 */
@Service
public class Pop3Service {

    private static final Logger logger = LoggerFactory.getLogger(Pop3Service.class);

    @Autowired
    private MailConfig mailConfig;

    @Autowired
    private Session mailSession;

    /**
     * 获取指定时间后发送到目标邮箱的邮件
     * 
     * @param targetEmail 目标邮箱地址
     * @param afterTime 时间过滤条件
     * @return 邮件列表
     */
    public List<EmailResponse.EmailData> getEmailsAfterTime(String targetEmail, LocalDateTime afterTime) {
        List<EmailResponse.EmailData> emails = new ArrayList<>();
        Store store = null;
        Folder folder = null;

        try {
            logger.info("开始连接POP3服务器获取邮件 - 目标邮箱: {}, 时间过滤: {}", targetEmail, afterTime);

            // 连接到POP3服务器
            store = mailSession.getStore("pop3");
            store.connect(mailConfig.getPop3Host(), mailConfig.getUsername(), mailConfig.getPassword());

            // 打开收件箱
            folder = store.getFolder("INBOX");
            folder.open(Folder.READ_ONLY);

            // 获取邮件数量
            int messageCount = folder.getMessageCount();
            logger.info("邮箱中共有 {} 封邮件", messageCount);

            if (messageCount == 0) {
                logger.info("邮箱中没有邮件");
                return emails;
            }

            // 从最新的邮件开始检查（倒序）
            Message[] messages = folder.getMessages();
            int processedCount = 0;
            int matchedCount = 0;

            for (int i = messages.length - 1; i >= 0; i--) {
                try {
                    Message message = messages[i];
                    processedCount++;

                    // 获取邮件接收时间
                    Date receivedDate = message.getReceivedDate();
                    if (receivedDate == null) {
                        receivedDate = message.getSentDate();
                    }

                    if (receivedDate == null) {
                        logger.warn("邮件 {} 没有时间信息，跳过", i + 1);
                        continue;
                    }

                    LocalDateTime emailTime = receivedDate.toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();

                    // 时间过滤：只处理指定时间之后的邮件
                    if (emailTime.isBefore(afterTime)) {
                        logger.debug("邮件时间 {} 早于过滤时间 {}，停止处理", emailTime, afterTime);
                        break;
                    }

                    // 检查邮件是否发送到目标邮箱
                    if (isEmailForTarget(message, targetEmail)) {
                        EmailResponse.EmailData emailData = parseEmailContent(message);
                        if (emailData != null) {
                            emails.add(emailData);
                            matchedCount++;
                            logger.info("找到匹配邮件: 发件人={}, 主题={}, 时间={}", 
                                    emailData.getFrom(), emailData.getSubject(), emailData.getReceivedTime());
                        }
                    }

                    // 性能优化：限制处理邮件数量
                    if (processedCount >= 100) {
                        logger.info("已处理 {} 封邮件，达到限制", processedCount);
                        break;
                    }

                } catch (Exception e) {
                    logger.error("处理邮件 {} 时发生错误: {}", i + 1, e.getMessage());
                }
            }

            logger.info("邮件获取完成 - 处理: {} 封, 匹配: {} 封", processedCount, matchedCount);

        } catch (Exception e) {
            logger.error("获取邮件时发生错误: {}", e.getMessage(), e);
        } finally {
            // 关闭连接
            try {
                if (folder != null && folder.isOpen()) {
                    folder.close(false);
                }
                if (store != null && store.isConnected()) {
                    store.close();
                }
            } catch (Exception e) {
                logger.error("关闭邮件连接时发生错误: {}", e.getMessage());
            }
        }

        return emails;
    }

    /**
     * 检查邮件是否发送到目标邮箱
     */
    private boolean isEmailForTarget(Message message, String targetEmail) throws MessagingException {
        // 检查收件人地址
        Address[] recipients = message.getAllRecipients();
        if (recipients != null) {
            for (Address recipient : recipients) {
                String recipientStr = recipient.toString().toLowerCase();
                if (recipientStr.contains(targetEmail.toLowerCase())) {
                    return true;
                }
            }
        }

        // 检查邮件主题和内容中是否包含目标邮箱（转发邮件的情况）
        String subject = message.getSubject();
        if (subject != null && subject.toLowerCase().contains(targetEmail.toLowerCase())) {
            return true;
        }

        return false;
    }

    /**
     * 解析邮件内容
     */
    private EmailResponse.EmailData parseEmailContent(Message message) {
        try {
            String from = getFromAddress(message);
            String subject = message.getSubject();
            String content = getTextContent(message);
            
            Date receivedDate = message.getReceivedDate();
            if (receivedDate == null) {
                receivedDate = message.getSentDate();
            }
            
            LocalDateTime receivedTime = receivedDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();

            Address[] recipients = message.getAllRecipients();
            String to = recipients != null && recipients.length > 0 ? recipients[0].toString() : "";

            return new EmailResponse.EmailData(from, subject, content, receivedTime, to);

        } catch (Exception e) {
            logger.error("解析邮件内容时发生错误: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取发件人地址
     */
    private String getFromAddress(Message message) throws MessagingException {
        Address[] fromAddresses = message.getFrom();
        if (fromAddresses != null && fromAddresses.length > 0) {
            return fromAddresses[0].toString();
        }
        return "未知发件人";
    }

    /**
     * 获取邮件文本内容
     */
    private String getTextContent(Message message) throws MessagingException, IOException {
        if (message.isMimeType("text/plain")) {
            return (String) message.getContent();
        } else if (message.isMimeType("text/html")) {
            return (String) message.getContent();
        } else if (message.isMimeType("multipart/*")) {
            return getTextFromMultipart((MimeMultipart) message.getContent());
        } else {
            return "不支持的邮件格式";
        }
    }

    /**
     * 从多部分邮件中提取文本内容
     */
    private String getTextFromMultipart(MimeMultipart multipart) throws MessagingException, IOException {
        StringBuilder result = new StringBuilder();
        int count = multipart.getCount();
        
        for (int i = 0; i < count; i++) {
            BodyPart bodyPart = multipart.getBodyPart(i);
            
            if (bodyPart.isMimeType("text/plain")) {
                result.append(bodyPart.getContent().toString());
            } else if (bodyPart.isMimeType("text/html")) {
                result.append(bodyPart.getContent().toString());
            } else if (bodyPart.isMimeType("multipart/*")) {
                result.append(getTextFromMultipart((MimeMultipart) bodyPart.getContent()));
            }
        }
        
        return result.toString();
    }
}
