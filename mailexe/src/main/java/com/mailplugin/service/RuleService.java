package com.mailplugin.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.mailplugin.model.ParseRule;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 解析规则服务类
 * 
 * 功能说明：
 * - 管理共享解析规则
 * - 提供规则的增删改查功能
 * - 持久化存储到JSON文件
 * - 支持并发访问
 */
@Service
public class RuleService {

    private static final Logger logger = LoggerFactory.getLogger(RuleService.class);

    private static final String RULES_FILE_PATH = "src/main/resources/shared-rules.json";
    private static final String API_KEY = "mailplugin_api_key_2024";

    private final ObjectMapper objectMapper;
    private final ConcurrentMap<String, ParseRule> rulesCache = new ConcurrentHashMap<>();

    public RuleService() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
    }

    /**
     * 初始化服务，加载现有规则
     */
    @PostConstruct
    public void init() {
        loadRulesFromFile();
        logger.info("解析规则服务初始化完成，加载规则数量: {}", rulesCache.size());
    }

    /**
     * 获取所有共享解析规则
     * 
     * @return 规则列表
     */
    public List<ParseRule> getAllSharedRules() {
        List<ParseRule> sharedRules = new ArrayList<>();
        
        for (ParseRule rule : rulesCache.values()) {
            if (rule.isShared()) {
                sharedRules.add(rule);
            }
        }
        
        logger.info("获取共享规则，数量: {}", sharedRules.size());
        return sharedRules;
    }

    /**
     * 根据发件人邮箱获取解析规则
     * 
     * @param senderEmail 发件人邮箱
     * @return 匹配的规则列表
     */
    public List<ParseRule> getRulesBySender(String senderEmail) {
        List<ParseRule> matchingRules = new ArrayList<>();
        
        for (ParseRule rule : rulesCache.values()) {
            if (rule.isShared() && 
                rule.getSenderEmail() != null && 
                rule.getSenderEmail().equalsIgnoreCase(senderEmail)) {
                matchingRules.add(rule);
            }
        }
        
        logger.debug("根据发件人 {} 找到规则数量: {}", senderEmail, matchingRules.size());
        return matchingRules;
    }

    /**
     * 添加新的解析规则
     * 
     * @param rule 解析规则
     * @param apiKey API密钥
     * @return 操作结果
     */
    public boolean addRule(ParseRule rule, String apiKey) {
        try {
            // 验证API密钥
            if (!isValidApiKey(apiKey)) {
                logger.warn("无效的API密钥，拒绝添加规则");
                return false;
            }

            // 验证规则数据
            if (!isValidRule(rule)) {
                logger.warn("无效的规则数据: {}", rule);
                return false;
            }

            // 生成规则ID
            if (rule.getId() == null || rule.getId().isEmpty()) {
                rule.setId(UUID.randomUUID().toString());
            }

            // 设置创建时间
            rule.setCreatedTime(LocalDateTime.now());

            // 添加到缓存
            rulesCache.put(rule.getId(), rule);

            // 保存到文件
            saveRulesToFile();

            logger.info("添加解析规则成功 - ID: {}, 发件人: {}, 平台: {}", 
                    rule.getId(), rule.getSenderEmail(), rule.getPlatform());

            return true;

        } catch (Exception e) {
            logger.error("添加解析规则时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 更新解析规则
     * 
     * @param rule 解析规则
     * @param apiKey API密钥
     * @return 操作结果
     */
    public boolean updateRule(ParseRule rule, String apiKey) {
        try {
            // 验证API密钥
            if (!isValidApiKey(apiKey)) {
                logger.warn("无效的API密钥，拒绝更新规则");
                return false;
            }

            // 验证规则存在
            if (rule.getId() == null || !rulesCache.containsKey(rule.getId())) {
                logger.warn("规则不存在，无法更新: {}", rule.getId());
                return false;
            }

            // 验证规则数据
            if (!isValidRule(rule)) {
                logger.warn("无效的规则数据: {}", rule);
                return false;
            }

            // 更新缓存
            rulesCache.put(rule.getId(), rule);

            // 保存到文件
            saveRulesToFile();

            logger.info("更新解析规则成功 - ID: {}", rule.getId());

            return true;

        } catch (Exception e) {
            logger.error("更新解析规则时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除解析规则
     * 
     * @param ruleId 规则ID
     * @param apiKey API密钥
     * @return 操作结果
     */
    public boolean deleteRule(String ruleId, String apiKey) {
        try {
            // 验证API密钥
            if (!isValidApiKey(apiKey)) {
                logger.warn("无效的API密钥，拒绝删除规则");
                return false;
            }

            // 删除规则
            ParseRule removed = rulesCache.remove(ruleId);
            if (removed == null) {
                logger.warn("规则不存在，无法删除: {}", ruleId);
                return false;
            }

            // 保存到文件
            saveRulesToFile();

            logger.info("删除解析规则成功 - ID: {}", ruleId);

            return true;

        } catch (Exception e) {
            logger.error("删除解析规则时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证API密钥
     */
    private boolean isValidApiKey(String apiKey) {
        return API_KEY.equals(apiKey);
    }

    /**
     * 验证解析规则数据
     */
    private boolean isValidRule(ParseRule rule) {
        return rule != null &&
               rule.getSenderEmail() != null && !rule.getSenderEmail().trim().isEmpty() &&
               rule.getPattern() != null && !rule.getPattern().trim().isEmpty() &&
               rule.getPatternType() != null && !rule.getPatternType().trim().isEmpty();
    }

    /**
     * 从文件加载解析规则
     */
    private void loadRulesFromFile() {
        try {
            File file = new File(RULES_FILE_PATH);
            if (!file.exists()) {
                logger.info("规则文件不存在，创建新文件: {}", RULES_FILE_PATH);
                saveRulesToFile();
                return;
            }

            List<ParseRule> rules = objectMapper.readValue(file, new TypeReference<List<ParseRule>>() {});
            
            rulesCache.clear();
            for (ParseRule rule : rules) {
                rulesCache.put(rule.getId(), rule);
            }

            logger.info("从文件加载解析规则完成，数量: {}", rules.size());

        } catch (Exception e) {
            logger.error("加载解析规则文件时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 保存解析规则到文件
     */
    private void saveRulesToFile() {
        try {
            File file = new File(RULES_FILE_PATH);
            
            // 确保目录存在
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            List<ParseRule> rules = new ArrayList<>(rulesCache.values());
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(file, rules);

            logger.debug("保存解析规则到文件完成，数量: {}", rules.size());

        } catch (IOException e) {
            logger.error("保存解析规则文件时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取规则统计信息
     */
    public int getTotalRulesCount() {
        return rulesCache.size();
    }

    public int getSharedRulesCount() {
        return (int) rulesCache.values().stream().filter(ParseRule::isShared).count();
    }
}
