package com.mailplugin.service;

import com.mailplugin.model.EmailRequest;
import com.mailplugin.model.EmailResponse;
import com.mailplugin.util.EmailUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 邮件业务服务类
 * 
 * 功能说明：
 * - 管理用户会话数据
 * - 处理邮件获取请求
 * - 协调POP3服务和API响应
 * - 实现并发处理和数据隔离
 */
@Service
public class EmailService {

    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);

    @Autowired
    private Pop3Service pop3Service;

    @Autowired
    private PerformanceService performanceService;

    // 用户会话数据存储（内存中临时存储）
    private final Map<String, UserSession> userSessions = new ConcurrentHashMap<>();

    // API密钥（生产环境中应该从配置文件读取）
    private static final String API_KEY = "mailplugin_api_key_2024";

    /**
     * 用户会话数据类
     */
    private static class UserSession {
        private String userId;
        private String tempEmail;
        private LocalDateTime generateTime;
        private LocalDateTime lastAccessTime;

        public UserSession(String userId, String tempEmail, LocalDateTime generateTime) {
            this.userId = userId;
            this.tempEmail = tempEmail;
            this.generateTime = generateTime;
            this.lastAccessTime = LocalDateTime.now();
        }

        // Getter方法
        public String getUserId() { return userId; }
        public String getTempEmail() { return tempEmail; }
        public LocalDateTime getGenerateTime() { return generateTime; }
        public LocalDateTime getLastAccessTime() { return lastAccessTime; }

        public void updateLastAccessTime() {
            this.lastAccessTime = LocalDateTime.now();
        }
    }

    /**
     * 处理临时邮箱生成请求
     * 
     * @param request 邮件请求
     * @return 处理结果
     */
    public EmailResponse generateTempEmail(EmailRequest request) {
        try {
            logger.info("处理临时邮箱生成请求: {}", request);

            // 验证API密钥
            if (!isValidApiKey(request.getApiKey())) {
                logger.warn("无效的API密钥: {}", request.getUserId());
                return new EmailResponse("无效的API密钥");
            }

            // 验证请求参数
            if (request.getUserId() == null || request.getTempEmail() == null || request.getGenerateTime() == null) {
                logger.warn("请求参数不完整: {}", request);
                return new EmailResponse("请求参数不完整");
            }

            // 存储用户会话数据
            UserSession session = new UserSession(
                    request.getUserId(),
                    request.getTempEmail(),
                    request.getGenerateTime()
            );
            userSessions.put(request.getUserId(), session);

            logger.info("用户会话创建成功 - 用户ID: {}, 临时邮箱: {}", 
                    request.getUserId(), request.getTempEmail());

            // 清理过期会话（异步执行）
            cleanExpiredSessions();

            return new EmailResponse("临时邮箱生成成功", true);

        } catch (Exception e) {
            logger.error("处理临时邮箱生成请求时发生错误: {}", e.getMessage(), e);
            return new EmailResponse("服务器内部错误");
        }
    }

    /**
     * 获取用户邮件
     * 
     * @param userId 用户ID
     * @param apiKey API密钥
     * @return 邮件响应
     */
    public EmailResponse getUserEmails(String userId, String apiKey) {
        String requestId = EmailUtils.generateUserId();
        performanceService.recordRequestStart(requestId);

        try {
            logger.info("处理邮件获取请求 - 用户ID: {}, 请求ID: {}", userId, requestId);

            // 验证API密钥
            if (!isValidApiKey(apiKey)) {
                logger.warn("无效的API密钥: {}", userId);
                performanceService.recordRequestEnd(requestId, false);
                return new EmailResponse("无效的API密钥");
            }

            // 检查缓存
            Object cachedData = performanceService.getCachedEmailData(userId);
            if (cachedData instanceof EmailResponse) {
                logger.info("返回缓存的邮件数据 - 用户ID: {}", userId);
                performanceService.recordRequestEnd(requestId, true);
                return (EmailResponse) cachedData;
            }

            // 获取用户会话
            UserSession session = userSessions.get(userId);
            if (session == null) {
                logger.warn("用户会话不存在: {}", userId);
                performanceService.recordRequestEnd(requestId, false);
                return new EmailResponse("用户会话不存在，请先生成临时邮箱");
            }

            // 更新最后访问时间
            session.updateLastAccessTime();

            // 获取邮件数据
            List<EmailResponse.EmailData> emails = pop3Service.getEmailsAfterTime(
                    session.getTempEmail(),
                    session.getGenerateTime()
            );

            EmailResponse response = new EmailResponse(emails);

            // 缓存结果（缓存2分钟）
            if (emails.size() > 0) {
                performanceService.cacheEmailData(userId, response, 2);
            }

            logger.info("邮件获取完成 - 用户ID: {}, 邮件数量: {}, 请求ID: {}", userId, emails.size(), requestId);
            performanceService.recordRequestEnd(requestId, true);

            return response;

        } catch (Exception e) {
            logger.error("获取用户邮件时发生错误: {}", e.getMessage(), e);
            performanceService.recordRequestEnd(requestId, false);
            return new EmailResponse("获取邮件失败: " + e.getMessage());
        }
    }

    /**
     * 异步获取用户邮件
     * 
     * @param userId 用户ID
     * @param apiKey API密钥
     * @return 异步邮件响应
     */
    @Async("mailTaskExecutor")
    public CompletableFuture<EmailResponse> getUserEmailsAsync(String userId, String apiKey) {
        return CompletableFuture.completedFuture(getUserEmails(userId, apiKey));
    }

    /**
     * 验证API密钥
     */
    private boolean isValidApiKey(String apiKey) {
        return API_KEY.equals(apiKey);
    }

    /**
     * 清理过期的用户会话
     * 会话超过30分钟未访问将被清理
     */
    @Async("mailTaskExecutor")
    public void cleanExpiredSessions() {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusMinutes(30);
            int cleanedCount = 0;

            for (Map.Entry<String, UserSession> entry : userSessions.entrySet()) {
                UserSession session = entry.getValue();
                if (session.getLastAccessTime().isBefore(expireTime)) {
                    userSessions.remove(entry.getKey());
                    cleanedCount++;
                }
            }

            if (cleanedCount > 0) {
                logger.info("清理过期会话完成 - 清理数量: {}, 剩余会话: {}", 
                        cleanedCount, userSessions.size());
            }

        } catch (Exception e) {
            logger.error("清理过期会话时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 获取当前活跃会话数量
     */
    public int getActiveSessionCount() {
        return userSessions.size();
    }

    /**
     * 手动清理指定用户会话
     */
    public void removeUserSession(String userId) {
        UserSession removed = userSessions.remove(userId);
        if (removed != null) {
            logger.info("手动清理用户会话: {}", userId);
        }
    }
}
