package com.mailplugin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 临时邮箱服务主启动类
 * 
 * 功能说明：
 * - 启动Spring Boot应用
 * - 启用异步处理支持
 * - 启用定时任务支持
 * - 配置生产环境优化参数
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
public class MailPluginApplication {

    private static final Logger logger = LoggerFactory.getLogger(MailPluginApplication.class);

    public static void main(String[] args) {
        try {
            // 设置系统属性以优化性能
            System.setProperty("spring.main.lazy-initialization", "true");
            System.setProperty("server.tomcat.threads.max", "50");
            System.setProperty("server.tomcat.threads.min-spare", "10");
            
            // 启动应用
            SpringApplication app = new SpringApplication(MailPluginApplication.class);
            
            // 生产环境优化配置
            app.setAdditionalProfiles("production");
            
            logger.info("正在启动临时邮箱服务...");
            app.run(args);
            logger.info("临时邮箱服务启动成功！");
            
        } catch (Exception e) {
            logger.error("应用启动失败: {}", e.getMessage(), e);
            System.exit(1);
        }
    }
}
