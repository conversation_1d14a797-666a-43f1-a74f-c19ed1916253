package com.mailplugin.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 安全工具类
 * 
 * 功能说明：
 * - API密钥验证
 * - 请求频率限制
 * - 安全哈希计算
 * - IP地址验证
 */
public class SecurityUtils {

    private static final Logger logger = LoggerFactory.getLogger(SecurityUtils.class);

    // API密钥（生产环境应从配置文件读取）
    private static final String API_KEY = "mailplugin_api_key_2024";
    private static final String API_KEY_HASH = hashString(API_KEY);

    // 请求频率限制存储
    private static final Map<String, RequestInfo> requestTracker = new ConcurrentHashMap<>();

    // 请求频率限制配置
    private static final int MAX_REQUESTS_PER_MINUTE = 60;
    private static final int MAX_REQUESTS_PER_HOUR = 300;

    /**
     * 请求信息类
     */
    private static class RequestInfo {
        private int requestsInLastMinute;
        private int requestsInLastHour;
        private LocalDateTime lastRequestTime;
        private LocalDateTime hourStartTime;

        public RequestInfo() {
            this.requestsInLastMinute = 1;
            this.requestsInLastHour = 1;
            this.lastRequestTime = LocalDateTime.now();
            this.hourStartTime = LocalDateTime.now();
        }

        public void updateRequest() {
            LocalDateTime now = LocalDateTime.now();
            
            // 检查是否需要重置分钟计数
            if (ChronoUnit.MINUTES.between(lastRequestTime, now) >= 1) {
                requestsInLastMinute = 1;
            } else {
                requestsInLastMinute++;
            }
            
            // 检查是否需要重置小时计数
            if (ChronoUnit.HOURS.between(hourStartTime, now) >= 1) {
                requestsInLastHour = 1;
                hourStartTime = now;
            } else {
                requestsInLastHour++;
            }
            
            lastRequestTime = now;
        }

        public boolean isRateLimited() {
            return requestsInLastMinute > MAX_REQUESTS_PER_MINUTE || 
                   requestsInLastHour > MAX_REQUESTS_PER_HOUR;
        }
    }

    /**
     * 验证API密钥
     * 
     * @param apiKey 待验证的API密钥
     * @return 是否有效
     */
    public static boolean isValidApiKey(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            logger.warn("API密钥为空");
            return false;
        }

        boolean isValid = API_KEY.equals(apiKey.trim());
        
        if (!isValid) {
            logger.warn("无效的API密钥: {}", maskApiKey(apiKey));
        }
        
        return isValid;
    }

    /**
     * 验证API密钥（哈希比较）
     * 
     * @param apiKey 待验证的API密钥
     * @return 是否有效
     */
    public static boolean isValidApiKeyHash(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return false;
        }

        String inputHash = hashString(apiKey.trim());
        return API_KEY_HASH.equals(inputHash);
    }

    /**
     * 检查请求频率限制
     * 
     * @param clientId 客户端标识（IP地址或用户ID）
     * @return 是否被限制
     */
    public static boolean isRateLimited(String clientId) {
        if (clientId == null || clientId.trim().isEmpty()) {
            return true;
        }

        RequestInfo requestInfo = requestTracker.computeIfAbsent(clientId, k -> new RequestInfo());
        
        if (requestInfo.isRateLimited()) {
            logger.warn("客户端 {} 请求频率超限", clientId);
            return true;
        }
        
        requestInfo.updateRequest();
        return false;
    }

    /**
     * 计算字符串的SHA-256哈希值
     * 
     * @param input 输入字符串
     * @return 哈希值（Base64编码）
     */
    public static String hashString(String input) {
        if (input == null) {
            return null;
        }

        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            logger.error("SHA-256算法不可用", e);
            return null;
        }
    }

    /**
     * 掩码API密钥（用于日志记录）
     * 
     * @param apiKey API密钥
     * @return 掩码后的API密钥
     */
    public static String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() <= 8) {
            return "***";
        }
        
        return apiKey.substring(0, 4) + "***" + apiKey.substring(apiKey.length() - 4);
    }

    /**
     * 验证IP地址格式
     * 
     * @param ip IP地址
     * @return 是否有效
     */
    public static boolean isValidIpAddress(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 检查IP地址是否在白名单中
     * 
     * @param ip IP地址
     * @return 是否在白名单中
     */
    public static boolean isIpWhitelisted(String ip) {
        // 生产环境中应该从配置文件读取白名单
        String[] whitelist = {
            "127.0.0.1",        // 本地回环
            "localhost",        // 本地主机
            "************"      // 服务器IP
        };

        if (ip == null) {
            return false;
        }

        for (String whitelistedIp : whitelist) {
            if (whitelistedIp.equals(ip)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 生成安全的随机字符串
     * 
     * @param length 长度
     * @return 随机字符串
     */
    public static String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            int index = (int) (Math.random() * chars.length());
            sb.append(chars.charAt(index));
        }
        
        return sb.toString();
    }

    /**
     * 验证用户ID格式
     * 
     * @param userId 用户ID
     * @return 是否有效
     */
    public static boolean isValidUserId(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }

        // 用户ID格式：时间戳_随机数
        String[] parts = userId.split("_");
        if (parts.length != 2) {
            return false;
        }

        try {
            Long.parseLong(parts[0]); // 验证时间戳部分
            Integer.parseInt(parts[1]); // 验证随机数部分
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 清理过期的请求跟踪记录
     */
    public static void cleanExpiredRequestTracking() {
        LocalDateTime expireTime = LocalDateTime.now().minusHours(2);
        
        requestTracker.entrySet().removeIf(entry -> {
            RequestInfo info = entry.getValue();
            return info.lastRequestTime.isBefore(expireTime);
        });
        
        logger.debug("清理过期请求跟踪记录完成，剩余记录数: {}", requestTracker.size());
    }

    /**
     * 获取当前请求跟踪统计
     * 
     * @return 统计信息
     */
    public static Map<String, Integer> getRequestTrackingStats() {
        Map<String, Integer> stats = new ConcurrentHashMap<>();
        stats.put("totalClients", requestTracker.size());
        
        int limitedClients = 0;
        for (RequestInfo info : requestTracker.values()) {
            if (info.isRateLimited()) {
                limitedClients++;
            }
        }
        stats.put("limitedClients", limitedClients);
        
        return stats;
    }
}
