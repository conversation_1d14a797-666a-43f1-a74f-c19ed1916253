package com.mailplugin.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 邮件工具类
 * 
 * 功能说明：
 * - 邮件地址验证
 * - 邮件内容处理
 * - 时间格式转换
 * - 文本清理和格式化
 */
public class EmailUtils {

    private static final Logger logger = LoggerFactory.getLogger(EmailUtils.class);

    // 邮件地址正则表达式
    private static final String EMAIL_PATTERN = 
            "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@" +
            "(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";

    private static final Pattern EMAIL_REGEX = Pattern.compile(EMAIL_PATTERN);

    // 时间格式化器
    private static final DateTimeFormatter DATE_TIME_FORMATTER = 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 验证邮件地址格式
     * 
     * @param email 邮件地址
     * @return 是否有效
     */
    public static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        
        Matcher matcher = EMAIL_REGEX.matcher(email.trim());
        return matcher.matches();
    }

    /**
     * 提取邮件地址的域名部分
     * 
     * @param email 邮件地址
     * @return 域名
     */
    public static String extractDomain(String email) {
        if (!isValidEmail(email)) {
            return null;
        }
        
        int atIndex = email.indexOf('@');
        if (atIndex > 0 && atIndex < email.length() - 1) {
            return email.substring(atIndex + 1).toLowerCase();
        }
        
        return null;
    }

    /**
     * 检查邮件是否来自指定域名
     * 
     * @param email 邮件地址
     * @param domain 目标域名
     * @return 是否匹配
     */
    public static boolean isFromDomain(String email, String domain) {
        String emailDomain = extractDomain(email);
        return emailDomain != null && emailDomain.equalsIgnoreCase(domain);
    }

    /**
     * 清理邮件内容，移除多余的空白字符
     * 
     * @param content 原始内容
     * @return 清理后的内容
     */
    public static String cleanEmailContent(String content) {
        if (content == null) {
            return "";
        }
        
        // 移除HTML标签（简单处理）
        String cleaned = content.replaceAll("<[^>]+>", "");
        
        // 移除多余的空白字符
        cleaned = cleaned.replaceAll("\\s+", " ");
        
        // 移除首尾空白
        cleaned = cleaned.trim();
        
        return cleaned;
    }

    /**
     * 截取邮件内容预览
     * 
     * @param content 邮件内容
     * @param maxLength 最大长度
     * @return 预览内容
     */
    public static String getContentPreview(String content, int maxLength) {
        if (content == null || content.isEmpty()) {
            return "";
        }
        
        String cleaned = cleanEmailContent(content);
        
        if (cleaned.length() <= maxLength) {
            return cleaned;
        }
        
        return cleaned.substring(0, maxLength) + "...";
    }

    /**
     * 格式化时间为字符串
     * 
     * @param dateTime 时间对象
     * @return 格式化的时间字符串
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        
        return dateTime.format(DATE_TIME_FORMATTER);
    }

    /**
     * 解析时间字符串
     * 
     * @param dateTimeStr 时间字符串
     * @return 时间对象
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            return LocalDateTime.parse(dateTimeStr.trim(), DATE_TIME_FORMATTER);
        } catch (Exception e) {
            logger.warn("解析时间字符串失败: {}", dateTimeStr);
            return null;
        }
    }

    /**
     * 生成用户ID（时间戳 + 随机数）
     * 
     * @return 用户ID
     */
    public static String generateUserId() {
        long timestamp = System.currentTimeMillis();
        int random = (int) (Math.random() * 10000);
        return timestamp + "_" + random;
    }

    /**
     * 验证临时邮箱地址格式
     * 
     * @param tempEmail 临时邮箱地址
     * @param expectedDomain 期望的域名
     * @return 是否有效
     */
    public static boolean isValidTempEmail(String tempEmail, String expectedDomain) {
        if (!isValidEmail(tempEmail)) {
            return false;
        }
        
        String domain = extractDomain(tempEmail);
        if (domain == null || !domain.equalsIgnoreCase(expectedDomain)) {
            return false;
        }
        
        // 检查用户名部分长度（8-12位）
        int atIndex = tempEmail.indexOf('@');
        String username = tempEmail.substring(0, atIndex);
        
        return username.length() >= 8 && username.length() <= 12;
    }

    /**
     * 掩码邮件地址（用于日志记录）
     * 
     * @param email 邮件地址
     * @return 掩码后的邮件地址
     */
    public static String maskEmail(String email) {
        if (!isValidEmail(email)) {
            return email;
        }
        
        int atIndex = email.indexOf('@');
        String username = email.substring(0, atIndex);
        String domain = email.substring(atIndex);
        
        if (username.length() <= 2) {
            return email;
        }
        
        String maskedUsername = username.charAt(0) + "***" + username.charAt(username.length() - 1);
        return maskedUsername + domain;
    }

    /**
     * 检查邮件内容是否包含验证码模式
     * 
     * @param content 邮件内容
     * @return 是否包含验证码
     */
    public static boolean containsVerificationCode(String content) {
        if (content == null || content.isEmpty()) {
            return false;
        }
        
        // 常见验证码模式
        String[] patterns = {
            "\\b\\d{4,8}\\b",           // 4-8位数字
            "验证码[：:]?\\s*\\d+",      // 中文验证码
            "code[：:]?\\s*\\d+",       // 英文code
            "verification[：:]?\\s*\\d+" // verification
        };
        
        for (String pattern : patterns) {
            if (Pattern.compile(pattern, Pattern.CASE_INSENSITIVE).matcher(content).find()) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查邮件内容是否包含链接
     * 
     * @param content 邮件内容
     * @return 是否包含链接
     */
    public static boolean containsLink(String content) {
        if (content == null || content.isEmpty()) {
            return false;
        }
        
        // URL模式
        String urlPattern = "https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+";
        return Pattern.compile(urlPattern, Pattern.CASE_INSENSITIVE).matcher(content).find();
    }

    /**
     * 计算两个时间之间的秒数差
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 秒数差
     */
    public static long getSecondsBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        
        return java.time.Duration.between(start, end).getSeconds();
    }
}
