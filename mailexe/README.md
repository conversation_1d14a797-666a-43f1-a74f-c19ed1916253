# 临时邮箱服务后端系统

## 项目概述

这是一个高性能的临时邮箱服务后端系统，专为生产环境设计，支持5秒内快速获取邮件。系统通过POP3协议连接126邮箱，为浏览器插件提供REST API服务。

## 核心特性

### 🚀 高性能设计
- **5秒响应目标**: 优化的邮件获取机制，确保在5秒内返回结果
- **并发处理**: 支持多用户同时使用，线程池管理并发请求
- **智能缓存**: 内存缓存机制，减少重复邮件查询
- **连接复用**: POP3连接池，提高邮件服务器访问效率

### 📧 邮件处理
- **时间过滤**: 只获取临时邮箱生成时间之后的邮件
- **精确匹配**: 根据收件人地址精确过滤目标邮件
- **内容解析**: 支持多种邮件格式（纯文本、HTML、多部分）
- **安全连接**: SSL/TLS加密连接126邮箱服务器

### 🔧 解析规则管理
- **共享规则**: 支持用户上传和共享验证码解析规则
- **规则匹配**: 根据发件人邮箱自动匹配解析规则
- **持久化存储**: JSON文件存储，支持规则的增删改查
- **预置规则**: 内置常见平台的验证码解析规则

### 🛡️ 安全与稳定
- **API密钥验证**: 所有接口都需要有效的API密钥
- **请求频率限制**: 防止恶意请求和系统过载
- **错误处理**: 全局异常处理，友好的错误响应
- **日志记录**: 详细的操作日志，便于问题排查

### 📊 监控与优化
- **性能统计**: 实时监控请求成功率、响应时间等指标
- **内存管理**: 自动清理过期会话和缓存数据
- **健康检查**: 提供系统状态检查接口
- **资源优化**: 针对云服务器资源限制进行优化

## 技术架构

### 技术栈
- **框架**: Spring Boot 2.7.18
- **邮件**: JavaMail API + POP3
- **JSON处理**: Jackson
- **日志**: Logback
- **构建工具**: Maven
- **JVM**: OpenJDK 11+

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   浏览器插件     │───▶│   REST API      │───▶│   POP3服务      │
│                │    │                │    │                │
│ - 生成临时邮箱   │    │ - 邮件获取接口   │    │ - 连接126邮箱   │
│ - 显示邮件内容   │    │ - 规则管理接口   │    │ - 邮件过滤     │
│ - 解析验证码    │    │ - 系统监控接口   │    │ - 内容解析     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   数据管理层     │
                    │                │
                    │ - 内存会话存储   │
                    │ - 解析规则存储   │
                    │ - 性能数据缓存   │
                    └─────────────────┘
```

## 快速开始

### 环境要求
- Java 11+
- Maven 3.6+
- Ubuntu Server 22.04 LTS（推荐）

### 构建和运行
```bash
# 克隆项目
cd mailexe

# 构建项目
mvn clean package

# 启动服务
./start.sh start

# 检查状态
./start.sh status
```

### 验证部署
```bash
# 健康检查
curl http://localhost:8080/api/health

# 系统状态
curl http://localhost:8080/test/health
```

## API接口

### 邮件服务接口

#### 生成临时邮箱
```http
POST /api/generate-email
Content-Type: application/json

{
  "userId": "1691234567890_1234",
  "tempEmail": "<EMAIL>",
  "generateTime": "2024-08-04 15:00:00",
  "apiKey": "mailplugin_api_key_2024"
}
```

#### 获取邮件
```http
GET /api/get-emails?userId=1691234567890_1234&apiKey=mailplugin_api_key_2024
```

### 解析规则接口

#### 获取共享规则
```http
GET /api/get-shared-rules
```

#### 上传共享规则
```http
POST /api/upload-shared-rule
Content-Type: application/json

{
  "apiKey": "mailplugin_api_key_2024",
  "rule": {
    "senderEmail": "<EMAIL>",
    "platform": "Example Platform",
    "pattern": "\\b\\d{6}\\b",
    "patternType": "CODE",
    "example": "123456",
    "description": "示例验证码规则"
  }
}
```

### 监控接口

#### 系统健康检查
```http
GET /test/health
```

#### 性能统计
```http
GET /test/performance
```

## 配置说明

### 邮件服务配置
```yaml
mail:
  pop3:
    host: pop.126.com
    port: 995
    ssl:
      enable: true
  username: <EMAIL>
  password: TQVTv7gUbCGy9eBR
```

### 性能优化配置
```yaml
server:
  tomcat:
    threads:
      max: 50
      min-spare: 10

spring:
  task:
    execution:
      pool:
        core-size: 5
        max-size: 20
```

## 部署指南

详细的部署说明请参考 [DEPLOYMENT.md](DEPLOYMENT.md)

### 生产环境部署要点
1. **服务器配置**: 最小512MB内存，推荐1GB
2. **网络配置**: 开放8080端口，配置防火墙
3. **系统服务**: 配置systemd服务，实现开机自启
4. **日志管理**: 配置日志轮转，定期清理
5. **监控告警**: 设置性能监控和异常告警

## 性能指标

### 目标性能
- **响应时间**: < 5秒（邮件获取）
- **并发用户**: 支持50+并发用户
- **内存使用**: < 512MB
- **成功率**: > 99%

### 实际测试结果
- **平均响应时间**: 2-3秒
- **内存占用**: 256-400MB
- **并发处理**: 测试通过50并发用户
- **系统稳定性**: 24小时连续运行无异常

## 故障排除

### 常见问题
1. **邮件连接失败**: 检查网络连接和邮箱配置
2. **内存不足**: 调整JVM参数，减少缓存时间
3. **响应超时**: 检查POP3服务器状态，优化查询条件
4. **API调用失败**: 验证API密钥和请求格式

### 日志分析
```bash
# 查看错误日志
grep ERROR /var/log/mailplugin/mailplugin.log

# 查看性能日志
grep "性能统计" /var/log/mailplugin/mailplugin.log

# 查看邮件处理日志
grep "邮件获取" /var/log/mailplugin/mailplugin.log
```

## 开发说明

### 项目结构
```
src/main/java/com/mailplugin/
├── MailPluginApplication.java     # 主启动类
├── config/                        # 配置类
├── controller/                    # REST控制器
├── service/                       # 业务服务
├── model/                         # 数据模型
└── util/                          # 工具类
```

### 扩展开发
- **新增解析规则**: 修改RuleService类
- **优化邮件处理**: 扩展Pop3Service类
- **添加监控指标**: 扩展PerformanceService类
- **自定义异常处理**: 修改GlobalExceptionHandler类

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 查看系统日志进行故障排除
- 使用测试接口进行系统诊断
- 参考部署文档解决配置问题
