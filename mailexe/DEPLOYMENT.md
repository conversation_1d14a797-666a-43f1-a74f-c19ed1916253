# 临时邮箱服务部署指南

## 系统要求

### 服务器环境
- **操作系统**: Ubuntu Server 22.04 LTS 64bit
- **内存**: 最小512MB，推荐1GB
- **存储**: 最小2GB可用空间
- **网络**: 公网IP，开放8080端口

### 软件依赖
- **Java**: OpenJDK 11或更高版本
- **Maven**: 3.6+（用于构建）
- **Git**: 用于代码管理

## 部署步骤

### 1. 环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Java 11
sudo apt install openjdk-11-jdk -y

# 验证Java安装
java -version

# 安装Maven
sudo apt install maven -y

# 验证Maven安装
mvn -version

# 安装Git
sudo apt install git -y
```

### 2. 获取代码

```bash
# 创建应用目录
sudo mkdir -p /opt/mailplugin
sudo chown $USER:$USER /opt/mailplugin
cd /opt/mailplugin

# 克隆或上传代码到mailexe目录
# 如果使用Git：
# git clone <repository-url> mailexe

# 如果手动上传，确保mailexe目录包含所有源代码
```

### 3. 构建应用

```bash
cd /opt/mailplugin/mailexe

# 构建项目
mvn clean package -DskipTests

# 验证构建结果
ls -la target/mailexe-1.0.0.jar
```

### 4. 配置权限

```bash
# 设置启动脚本权限
chmod +x start.sh

# 创建日志目录
sudo mkdir -p /var/log/mailplugin
sudo chown $USER:$USER /var/log/mailplugin
```

### 5. 启动服务

```bash
# 启动应用
./start.sh start

# 检查状态
./start.sh status

# 查看日志
./start.sh logs
```

### 6. 验证部署

```bash
# 健康检查
curl http://localhost:8080/api/health

# 系统状态
curl http://localhost:8080/api/status

# 测试接口
curl http://localhost:8080/test/health
```

## 配置说明

### 应用配置文件
主要配置文件：`src/main/resources/application.yml`

#### 关键配置项：
```yaml
server:
  port: 8080  # 服务端口

mail:
  pop3:
    host: pop.126.com
    port: 995
  username: <EMAIL>
  password: TQVTv7gUbCGy9eBR

logging:
  file:
    name: /var/log/mailplugin/mailplugin.log
```

### 环境变量配置
可以通过环境变量覆盖配置：

```bash
export SPRING_PROFILES_ACTIVE=production
export SERVER_PORT=8080
export MAIL_USERNAME=<EMAIL>
export MAIL_PASSWORD=TQVTv7gUbCGy9eBR
```

## 防火墙配置

### Ubuntu UFW
```bash
# 启用防火墙
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许应用端口
sudo ufw allow 8080/tcp

# 查看状态
sudo ufw status
```

### 腾讯云安全组
在腾讯云控制台配置安全组规则：
- 入站规则：TCP 8080端口，来源：0.0.0.0/0
- 出站规则：允许所有

## 系统服务配置

### 创建systemd服务
```bash
sudo tee /etc/systemd/system/mailplugin.service > /dev/null <<EOF
[Unit]
Description=Mail Plugin Server
After=network.target

[Service]
Type=forking
User=$USER
WorkingDirectory=/opt/mailplugin/mailexe
ExecStart=/opt/mailplugin/mailexe/start.sh start
ExecStop=/opt/mailplugin/mailexe/start.sh stop
ExecReload=/opt/mailplugin/mailexe/start.sh restart
PIDFile=/var/run/mailplugin-server.pid
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 重载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable mailplugin

# 启动服务
sudo systemctl start mailplugin

# 查看状态
sudo systemctl status mailplugin
```

## 监控和维护

### 日志管理
```bash
# 查看实时日志
tail -f /var/log/mailplugin/mailplugin.log

# 查看错误日志
grep ERROR /var/log/mailplugin/mailplugin.log

# 日志轮转配置
sudo tee /etc/logrotate.d/mailplugin > /dev/null <<EOF
/var/log/mailplugin/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF
```

### 性能监控
```bash
# 查看系统状态
curl http://localhost:8080/test/performance

# 查看内存使用
curl http://localhost:8080/test/config

# 重置统计
curl -X POST "http://localhost:8080/test/reset-stats?apiKey=mailplugin_api_key_2024"
```

### 备份和恢复
```bash
# 备份配置文件
cp -r /opt/mailplugin/mailexe/src/main/resources /backup/

# 备份共享规则
cp /opt/mailplugin/mailexe/src/main/resources/shared-rules.json /backup/
```

## 故障排除

### 常见问题

1. **应用无法启动**
   ```bash
   # 检查Java版本
   java -version
   
   # 检查端口占用
   netstat -tlnp | grep 8080
   
   # 查看启动日志
   cat /var/log/mailplugin/startup.log
   ```

2. **邮件连接失败**
   ```bash
   # 测试网络连接
   telnet pop.126.com 995
   
   # 检查防火墙
   sudo ufw status
   
   # 查看邮件相关日志
   grep "mail" /var/log/mailplugin/mailplugin.log
   ```

3. **内存不足**
   ```bash
   # 调整JVM参数
   # 编辑start.sh中的JAVA_OPTS
   JAVA_OPTS="-Xms128m -Xmx256m -XX:+UseG1GC"
   ```

### 性能优化

1. **JVM调优**
   ```bash
   # 针对小内存服务器
   JAVA_OPTS="-Xms256m -Xmx512m -XX:+UseG1GC -XX:+UseStringDeduplication"
   ```

2. **应用配置优化**
   ```yaml
   server:
     tomcat:
       threads:
         max: 50
         min-spare: 10
   ```

## API接口文档

### 主要接口
- `POST /api/generate-email` - 生成临时邮箱
- `GET /api/get-emails` - 获取邮件
- `GET /api/get-shared-rules` - 获取共享规则
- `POST /api/upload-shared-rule` - 上传共享规则

### 测试接口
- `GET /test/health` - 系统健康检查
- `GET /test/performance` - 性能统计
- `GET /test/config` - 系统配置信息

## 安全注意事项

1. **API密钥管理**
   - 定期更换API密钥
   - 不要在日志中记录完整密钥

2. **网络安全**
   - 使用HTTPS（生产环境建议配置SSL证书）
   - 限制访问来源IP

3. **系统安全**
   - 定期更新系统和依赖
   - 监控异常访问

## 联系支持

如遇到部署问题，请检查：
1. 系统日志：`/var/log/mailplugin/mailplugin.log`
2. 启动日志：`/var/log/mailplugin/startup.log`
3. 系统状态：`curl http://localhost:8080/test/health`
