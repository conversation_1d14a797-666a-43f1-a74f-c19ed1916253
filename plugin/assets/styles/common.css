/* 通用样式 - 临时邮箱助手 */

/* 工具类 */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 12px; }
.mt-4 { margin-top: 16px; }

.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }

.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }

/* 按钮状态 */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-danger {
    background: #ef4444;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-success {
    background: #10b981;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-success:hover {
    background: #059669;
}

/* 状态指示 */
.status-online {
    color: #10b981;
}

.status-offline {
    color: #ef4444;
}

.status-warning {
    color: #f59e0b;
}

/* 加载动画 */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 提示框 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #374151;
    color: white;
    padding: 12px 16px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.toast.success {
    background: #10b981;
}

.toast.error {
    background: #ef4444;
}

.toast.warning {
    background: #f59e0b;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 表单验证 */
.form-group.error input,
.form-group.error textarea {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group.success input,
.form-group.success textarea {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.error-message {
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
}

.success-message {
    color: #10b981;
    font-size: 12px;
    margin-top: 4px;
}

/* 徽章 */
.badge {
    display: inline-block;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: 500;
    border-radius: 4px;
    background: #e5e7eb;
    color: #374151;
}

.badge.primary {
    background: #667eea;
    color: white;
}

.badge.success {
    background: #10b981;
    color: white;
}

.badge.warning {
    background: #f59e0b;
    color: white;
}

.badge.danger {
    background: #ef4444;
    color: white;
}

/* 分割线 */
.divider {
    height: 1px;
    background: #e5e7eb;
    margin: 16px 0;
}

/* 卡片阴影变化 */
.card-hover {
    transition: box-shadow 0.2s;
}

.card-hover:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 禁用状态 */
.disabled {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
}

/* 选中状态 */
.selected {
    background: #eff6ff;
    border-color: #3b82f6;
}

/* 高亮文本 */
.highlight {
    background: #fef3c7;
    padding: 1px 3px;
    border-radius: 2px;
}

/* 代码样式 */
.code {
    font-family: 'Courier New', monospace;
    background: #f3f4f6;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
}

/* 链接样式 */
.link {
    color: #3b82f6;
    text-decoration: none;
    cursor: pointer;
}

.link:hover {
    text-decoration: underline;
}

/* 截断文本 */
.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 响应式隐藏 */
@media (max-width: 320px) {
    .hide-mobile {
        display: none;
    }
}

/* 无选择 */
.no-select {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}
