/* CSS加载动画 - 替代loading.gif */

.loading-animation {
    width: 40px;
    height: 40px;
    position: relative;
    margin: 20px auto;
}

.loading-dots {
    display: inline-block;
    position: relative;
    width: 40px;
    height: 40px;
}

.loading-dots div {
    position: absolute;
    top: 16px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #667eea;
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.loading-dots div:nth-child(1) {
    left: 4px;
    animation: loading-dots1 0.6s infinite;
}

.loading-dots div:nth-child(2) {
    left: 4px;
    animation: loading-dots2 0.6s infinite;
}

.loading-dots div:nth-child(3) {
    left: 16px;
    animation: loading-dots2 0.6s infinite;
}

.loading-dots div:nth-child(4) {
    left: 28px;
    animation: loading-dots3 0.6s infinite;
}

@keyframes loading-dots1 {
    0% { transform: scale(0); }
    100% { transform: scale(1); }
}

@keyframes loading-dots3 {
    0% { transform: scale(1); }
    100% { transform: scale(0); }
}

@keyframes loading-dots2 {
    0% { transform: translate(0, 0); }
    100% { transform: translate(12px, 0); }
}

/* 脉冲加载动画 */
.loading-pulse {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #667eea;
    animation: loading-pulse 1.5s ease-in-out infinite;
}

@keyframes loading-pulse {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* 旋转加载动画 */
.loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 波浪加载动画 */
.loading-wave {
    display: inline-block;
    width: 40px;
    height: 40px;
    position: relative;
}

.loading-wave div {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #667eea;
    animation: loading-wave 1.2s linear infinite;
}

.loading-wave div:nth-child(1) { animation-delay: 0s; }
.loading-wave div:nth-child(2) { animation-delay: -0.1s; }
.loading-wave div:nth-child(3) { animation-delay: -0.2s; }
.loading-wave div:nth-child(4) { animation-delay: -0.3s; }
.loading-wave div:nth-child(5) { animation-delay: -0.4s; }
.loading-wave div:nth-child(6) { animation-delay: -0.5s; }

@keyframes loading-wave {
    0%, 40%, 100% {
        transform: translate(-50%, -50%) scale(0);
    }
    20% {
        transform: translate(-50%, -50%) scale(1);
    }
}
