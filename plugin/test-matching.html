<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则匹配算法测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }
        .test-case {
            margin-bottom: 15px;
            padding: 15px;
            background: #f9fafb;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
        .result.success { background: #d1fae5; color: #065f46; }
        .result.error { background: #fee2e2; color: #991b1b; }
        .result.info { background: #dbeafe; color: #1e40af; }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px 10px 0;
        }
        button:hover {
            background: #5a67d8;
        }
        .log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>规则匹配算法测试</h1>
        <p>测试新的邮箱匹配算法的准确性和性能</p>
    </div>

    <div class="test-section">
        <h2>测试用例</h2>
        <button onclick="runAllTests()">运行所有测试</button>
        <button onclick="clearLog()">清除日志</button>
        
        <div class="test-case">
            <h3>精确匹配测试</h3>
            <p><strong>发件人:</strong> <EMAIL></p>
            <p><strong>规则:</strong> <EMAIL></p>
            <p><strong>期望:</strong> 匹配成功，置信度 1.0，类型 exact</p>
            <div class="result" id="exact-match-result">等待测试...</div>
        </div>

        <div class="test-case">
            <h3>域名匹配测试</h3>
            <p><strong>发件人:</strong> <EMAIL></p>
            <p><strong>规则:</strong> <EMAIL></p>
            <p><strong>期望:</strong> 匹配成功，置信度 0.8，类型 domain</p>
            <div class="result" id="domain-match-result">等待测试...</div>
        </div>

        <div class="test-case">
            <h3>子域名匹配测试</h3>
            <p><strong>发件人:</strong> <EMAIL></p>
            <p><strong>规则:</strong> <EMAIL></p>
            <p><strong>期望:</strong> 匹配成功，置信度 0.7，类型 subdomain</p>
            <div class="result" id="subdomain-match-result">等待测试...</div>
        </div>

        <div class="test-case">
            <h3>包含匹配测试</h3>
            <p><strong>发件人:</strong> "GitHub" &lt;<EMAIL>&gt;</p>
            <p><strong>规则:</strong> <EMAIL></p>
            <p><strong>期望:</strong> 匹配成功，置信度 1.0，类型 exact（标准化后）</p>
            <div class="result" id="contains-match-result">等待测试...</div>
        </div>

        <div class="test-case">
            <h3>不匹配测试</h3>
            <p><strong>发件人:</strong> <EMAIL></p>
            <p><strong>规则:</strong> <EMAIL></p>
            <p><strong>期望:</strong> 匹配失败，置信度 0，类型 none</p>
            <div class="result" id="no-match-result">等待测试...</div>
        </div>
    </div>

    <div class="test-section">
        <h2>测试日志</h2>
        <div class="log" id="testLog">等待测试开始...\n</div>
    </div>

    <script type="module">
        // 模拟规则管理器类
        class MockRuleManager {
            extractDomain(email) {
                if (!email || typeof email !== 'string') {
                    return '';
                }
                
                const atIndex = email.lastIndexOf('@');
                if (atIndex === -1) {
                    return '';
                }
                
                return email.substring(atIndex + 1).toLowerCase().trim();
            }

            normalizeEmailAddress(email) {
                if (!email || typeof email !== 'string') {
                    return '';
                }
                
                let normalized = email.trim().toLowerCase();
                
                const emailMatch = normalized.match(/<([^>]+)>/);
                if (emailMatch) {
                    normalized = emailMatch[1];
                }
                
                normalized = normalized.replace(/^["']|["']$/g, '');
                
                return normalized;
            }

            calculateMatchConfidence(senderEmail, rule) {
                if (!senderEmail || !rule || !rule.senderEmail) {
                    return { isMatch: false, confidence: 0, matchType: 'none' };
                }

                const normalizedSender = this.normalizeEmailAddress(senderEmail);
                const normalizedRule = this.normalizeEmailAddress(rule.senderEmail);
                
                if (normalizedSender === normalizedRule) {
                    return { isMatch: true, confidence: 1.0, matchType: 'exact' };
                }
                
                const senderDomain = this.extractDomain(normalizedSender);
                const ruleDomain = this.extractDomain(normalizedRule);
                
                if (senderDomain && ruleDomain && senderDomain === ruleDomain) {
                    return { isMatch: true, confidence: 0.8, matchType: 'domain' };
                }
                
                if (senderDomain && ruleDomain) {
                    if (senderDomain.endsWith('.' + ruleDomain) || ruleDomain.endsWith('.' + senderDomain)) {
                        return { isMatch: true, confidence: 0.7, matchType: 'subdomain' };
                    }
                }
                
                if (normalizedSender.includes(normalizedRule) || normalizedRule.includes(normalizedSender)) {
                    return { isMatch: true, confidence: 0.5, matchType: 'contains' };
                }
                
                const senderParts = normalizedSender.split('@')[0].split(/[._-]/);
                const ruleParts = normalizedRule.split('@')[0].split(/[._-]/);
                
                const commonParts = senderParts.filter(part => 
                    part.length > 2 && ruleParts.some(rulePart => 
                        rulePart.includes(part) || part.includes(rulePart)
                    )
                );
                
                if (commonParts.length > 0) {
                    return { isMatch: true, confidence: 0.3, matchType: 'keyword' };
                }
                
                return { isMatch: false, confidence: 0, matchType: 'none' };
            }
        }

        const ruleManager = new MockRuleManager();

        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
        }

        function updateResult(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.className = `result ${success ? 'success' : 'error'}`;
            element.textContent = message;
        }

        function testExactMatch() {
            log('测试精确匹配...');
            const sender = '<EMAIL>';
            const rule = { senderEmail: '<EMAIL>' };
            
            const result = ruleManager.calculateMatchConfidence(sender, rule);
            const success = result.isMatch && result.confidence === 1.0 && result.matchType === 'exact';
            
            updateResult('exact-match-result', success, 
                `匹配: ${result.isMatch}, 置信度: ${result.confidence}, 类型: ${result.matchType}`);
            
            log(`精确匹配测试 ${success ? '通过' : '失败'}: ${JSON.stringify(result)}`);
        }

        function testDomainMatch() {
            log('测试域名匹配...');
            const sender = '<EMAIL>';
            const rule = { senderEmail: '<EMAIL>' };
            
            const result = ruleManager.calculateMatchConfidence(sender, rule);
            const success = result.isMatch && result.confidence === 0.8 && result.matchType === 'domain';
            
            updateResult('domain-match-result', success, 
                `匹配: ${result.isMatch}, 置信度: ${result.confidence}, 类型: ${result.matchType}`);
            
            log(`域名匹配测试 ${success ? '通过' : '失败'}: ${JSON.stringify(result)}`);
        }

        function testSubdomainMatch() {
            log('测试子域名匹配...');
            const sender = '<EMAIL>';
            const rule = { senderEmail: '<EMAIL>' };
            
            const result = ruleManager.calculateMatchConfidence(sender, rule);
            const success = result.isMatch && result.confidence === 0.7 && result.matchType === 'subdomain';
            
            updateResult('subdomain-match-result', success, 
                `匹配: ${result.isMatch}, 置信度: ${result.confidence}, 类型: ${result.matchType}`);
            
            log(`子域名匹配测试 ${success ? '通过' : '失败'}: ${JSON.stringify(result)}`);
        }

        function testContainsMatch() {
            log('测试包含匹配（标准化）...');
            const sender = '"GitHub" <<EMAIL>>';
            const rule = { senderEmail: '<EMAIL>' };
            
            const result = ruleManager.calculateMatchConfidence(sender, rule);
            const success = result.isMatch && result.confidence === 1.0 && result.matchType === 'exact';
            
            updateResult('contains-match-result', success, 
                `匹配: ${result.isMatch}, 置信度: ${result.confidence}, 类型: ${result.matchType}`);
            
            log(`包含匹配测试 ${success ? '通过' : '失败'}: ${JSON.stringify(result)}`);
        }

        function testNoMatch() {
            log('测试不匹配...');
            const sender = '<EMAIL>';
            const rule = { senderEmail: '<EMAIL>' };
            
            const result = ruleManager.calculateMatchConfidence(sender, rule);
            const success = !result.isMatch && result.confidence === 0 && result.matchType === 'none';
            
            updateResult('no-match-result', success, 
                `匹配: ${result.isMatch}, 置信度: ${result.confidence}, 类型: ${result.matchType}`);
            
            log(`不匹配测试 ${success ? '通过' : '失败'}: ${JSON.stringify(result)}`);
        }

        window.runAllTests = async function() {
            log('开始运行所有测试...');
            
            testExactMatch();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testDomainMatch();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testSubdomainMatch();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testContainsMatch();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testNoMatch();
            
            log('所有测试完成！');
        };

        window.clearLog = clearLog;

        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，准备测试新的匹配算法...');
        });
    </script>
</body>
</html>
