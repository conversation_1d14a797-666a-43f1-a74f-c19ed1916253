/**
 * 临时邮箱助手 - 侧边栏主逻辑
 */

import { emailGenerator } from '../modules/email-generator.js';
import { apiClient } from '../modules/api-client.js';
import { storageManager } from '../modules/storage-manager.js';
import { ruleManager } from '../modules/rule-manager.js';
import { emailParser } from '../modules/email-parser.js';

/**
 * 侧边栏应用类
 */
class SidepanelApp {
  constructor() {
    this.currentSession = null;
    this.isPolling = false;
    this.pollInterval = null;
    this.emails = [];
    this.localRules = [];
    this.sharedRules = [];
    
    this.initializeElements();
    this.bindEvents();
    this.loadInitialData();
  }

  /**
   * 初始化DOM元素引用
   */
  initializeElements() {
    // 状态指示器
    this.statusIndicator = document.getElementById('statusIndicator');
    this.statusDot = this.statusIndicator.querySelector('.status-dot');
    this.statusText = this.statusIndicator.querySelector('.status-text');
    
    // 邮箱生成区域
    this.generatedEmail = document.getElementById('generatedEmail');
    this.copyEmailBtn = document.getElementById('copyEmailBtn');
    this.generateBtn = document.getElementById('generateBtn');
    this.refreshBtn = document.getElementById('refreshBtn');
    this.generateTime = document.getElementById('generateTime');
    this.emailInfo = document.getElementById('emailInfo');
    
    // 邮件列表区域
    this.autoRefreshBtn = document.getElementById('autoRefreshBtn');
    this.emailCount = document.getElementById('emailCount');
    this.emailContainer = document.getElementById('emailContainer');
    this.emptyState = document.getElementById('emptyState');
    this.loadingState = document.getElementById('loadingState');
    this.emailListContent = document.getElementById('emailListContent');
    

    
    // 规则管理区域
    this.tabBtns = document.querySelectorAll('.tab-btn');
    this.localRulesTab = document.getElementById('localRulesTab');
    this.sharedRulesTab = document.getElementById('sharedRulesTab');
    this.createRuleTab = document.getElementById('createRuleTab');
    this.localRulesList = document.getElementById('localRulesList');
    this.sharedRulesList = document.getElementById('sharedRulesList');
    this.ruleForm = document.getElementById('ruleForm');
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 邮箱生成相关
    this.generateBtn.addEventListener('click', () => this.generateNewEmail());
    this.refreshBtn.addEventListener('click', () => this.refreshEmails());
    this.copyEmailBtn.addEventListener('click', () => this.copyEmail());
    
    // 自动刷新切换
    this.autoRefreshBtn.addEventListener('click', () => this.toggleAutoRefresh());
    
    // 规则管理标签切换
    this.tabBtns.forEach(btn => {
      btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
    });
    
    // 规则表单提交
    this.ruleForm.addEventListener('submit', (e) => this.handleRuleSubmit(e));

    // 规则保存按钮
    const saveLocalBtn = document.querySelector('.save-local-btn');
    const saveSharedBtn = document.querySelector('.save-shared-btn');

    if (saveLocalBtn) {
      saveLocalBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.handleRuleSubmit({ preventDefault: () => {}, submitter: saveLocalBtn });
      });
    }

    if (saveSharedBtn) {
      saveSharedBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.handleRuleSubmit({ preventDefault: () => {}, submitter: saveSharedBtn });
      });
    }
    
    // 邮件列表事件委托
    this.emailListContent.addEventListener('click', (e) => {
      // 处理邮件展开/折叠
      const emailHeader = e.target.closest('[data-toggle-email]');
      if (emailHeader) {
        const messageId = emailHeader.getAttribute('data-toggle-email');
        this.toggleEmailDetail(messageId);
        return;
      }

      // 处理复制按钮
      const copyBtn = e.target.closest('[data-copy-text]');
      if (copyBtn) {
        const text = copyBtn.getAttribute('data-copy-text');
        const message = copyBtn.getAttribute('data-copy-message');
        console.log('复制按钮点击:', { text, message });
        this.copyText(text, message);
        return;
      }
    });

    // 监听来自service worker的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'emailsUpdated') {
        this.handleEmailsUpdate(message.data);
      }
    });
  }

  /**
   * 加载初始数据
   */
  async loadInitialData() {
    try {
      this.updateStatus('loading', '检查服务器连接...');

      // 首先测试服务器连接
      const connectionTest = await apiClient.testConnection();
      if (!connectionTest.success) {
        this.updateStatus('error', '服务器连接失败');
        this.showToast('服务器连接失败，请检查服务器状态');
        console.error('服务器连接测试失败:', connectionTest);
        return;
      }

      this.updateStatus('loading', '初始化中...');

      // 初始化规则管理器
      await ruleManager.initialize();

      // 尝试恢复用户会话
      const sessionData = await storageManager.loadUserSession();
      if (sessionData && emailGenerator.restoreSession(sessionData)) {
        this.currentSession = emailGenerator.getCurrentSession();
        this.updateEmailDisplay();
        this.refreshBtn.disabled = false;

        // 自动获取邮件
        await this.refreshEmails();
      }

      // 加载本地规则
      this.localRules = await storageManager.loadParseRules();
      this.updateLocalRulesList();

      // 加载共享规则
      await this.loadSharedRules();

      this.updateStatus('ready', '就绪');
    } catch (error) {
      console.error('初始化失败:', error);
      this.updateStatus('error', '初始化失败: ' + error.message);
    }
  }

  /**
   * 生成新的临时邮箱
   */
  async generateNewEmail() {
    try {
      this.updateStatus('loading', '生成邮箱中...');
      this.generateBtn.disabled = true;
      
      // 停止当前轮询
      if (this.isPolling) {
        this.stopPolling();
      }
      
      // 生成新邮箱
      const emailData = emailGenerator.generateTempEmail();
      
      // 发送到后端
      const response = await apiClient.generateEmail(emailData);
      
      if (response.success) {
        this.currentSession = emailData;
        await storageManager.saveUserSession(emailData);
        
        this.updateEmailDisplay();
        this.refreshBtn.disabled = false;
        this.emails = [];
        this.updateEmailList();

        
        this.updateStatus('ready', '邮箱已生成');
        
        // 自动开始轮询
        this.startPolling();
      } else {
        throw new Error(response.message || '生成邮箱失败');
      }
    } catch (error) {
      console.error('生成邮箱失败:', error);
      this.updateStatus('error', '生成失败: ' + error.message);
    } finally {
      this.generateBtn.disabled = false;
    }
  }

  /**
   * 刷新邮件
   */
  async refreshEmails() {
    if (!this.currentSession) {
      return;
    }
    
    try {
      this.showLoading(true);
      
      const response = await apiClient.getEmails(this.currentSession.userId);
      
      if (response.success) {
        this.emails = response.emails || [];

        // 立即对每封邮件进行解析
        await this.parseEmailsWithRules();

        await storageManager.cacheEmails(this.currentSession.userId, this.emails);
        this.updateEmailList();

        this.updateStatus('ready', `已获取 ${this.emails.length} 封邮件`);
      } else {
        throw new Error(response.message || '获取邮件失败');
      }
    } catch (error) {
      console.error('刷新邮件失败:', error);
      this.updateStatus('error', '刷新失败: ' + error.message);
      
      // 尝试使用缓存数据
      const cachedEmails = await storageManager.getCachedEmails(this.currentSession.userId);
      if (cachedEmails) {
        this.emails = cachedEmails;
        await this.parseEmailsWithRules();
        this.updateEmailList();
      }
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 复制邮箱地址
   */
  async copyEmail() {
    if (!this.currentSession) {
      return;
    }
    
    try {
      await navigator.clipboard.writeText(this.currentSession.tempEmail);
      this.showToast('邮箱地址已复制');
    } catch (error) {
      console.error('复制失败:', error);
      this.showToast('复制失败');
    }
  }

  /**
   * 切换自动刷新
   */
  toggleAutoRefresh() {
    if (this.isPolling) {
      this.stopPolling();
    } else {
      this.startPolling();
    }
  }

  /**
   * 开始邮件轮询
   */
  startPolling() {
    if (!this.currentSession || this.isPolling) {
      return;
    }
    
    this.isPolling = true;
    this.autoRefreshBtn.classList.add('active');
    
    // 通知service worker开始轮询
    chrome.runtime.sendMessage({
      action: 'startPolling',
      data: { userId: this.currentSession.userId }
    });
    
    console.log('开始邮件轮询');
  }

  /**
   * 停止邮件轮询
   */
  stopPolling() {
    if (!this.isPolling) {
      return;
    }
    
    this.isPolling = false;
    this.autoRefreshBtn.classList.remove('active');
    
    // 通知service worker停止轮询
    chrome.runtime.sendMessage({
      action: 'stopPolling',
      data: { userId: this.currentSession?.userId }
    });
    
    console.log('停止邮件轮询');
  }

  /**
   * 处理邮件更新
   */
  async handleEmailsUpdate(data) {
    if (data.success && data.emails) {
      this.emails = data.emails;
      await this.parseEmailsWithRules();
      this.updateEmailList();

      // 只有当确实有新邮件时才显示通知
      if (data.isNewEmails && data.newEmailCount > 0) {
        this.showToast(`收到 ${data.newEmailCount} 封新邮件`);
      }
    }
  }

  /**
   * 更新邮箱显示
   */
  updateEmailDisplay() {
    if (this.currentSession) {
      this.generatedEmail.value = this.currentSession.tempEmail;
      this.generateTime.textContent = this.currentSession.generateTime;
      this.copyEmailBtn.disabled = false;
    } else {
      this.generatedEmail.value = '';
      this.generateTime.textContent = '-';
      this.copyEmailBtn.disabled = true;
    }
  }

  /**
   * 更新邮件列表显示
   */
  updateEmailList() {
    this.emailCount.textContent = `${this.emails.length} 封邮件`;
    
    if (this.emails.length === 0) {
      this.emptyState.style.display = 'flex';
      this.emailListContent.style.display = 'none';
    } else {
      this.emptyState.style.display = 'none';
      this.emailListContent.style.display = 'block';
      
      this.emailListContent.innerHTML = this.emails.map(email => this.createEmailItem(email)).join('');
    }
  }

  /**
   * 创建邮件项HTML
   */
  createEmailItem(email) {
    // 获取解析结果
    const parseResults = email.parseResults || { codes: [], links: [] };

    // 生成验证码HTML
    const codesHtml = parseResults.codes.length > 0
      ? parseResults.codes.map(code => {
          const codeValue = String(code.value || '').replace(/"/g, '&quot;');
          return `
          <div class="parse-result-item">
            <label>验证码:</label>
            <div class="result-display">
              <input type="text" value="${codeValue}" readonly>
              <button class="copy-btn" data-copy-text="${codeValue}" data-copy-message="验证码已复制" title="复制验证码">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                </svg>
              </button>
            </div>
          </div>`;
        }).join('')
      : '<div class="parse-result-item"><label>验证码:</label><span class="no-result">无</span></div>';

    // 生成链接HTML
    const linksHtml = parseResults.links.length > 0
      ? parseResults.links.map(link => {
          const linkValue = String(link.value || '').replace(/"/g, '&quot;');
          return `
          <div class="parse-result-item">
            <label>链接:</label>
            <div class="result-display">
              <input type="text" value="${linkValue}" readonly>
              <button class="copy-btn" data-copy-text="${linkValue}" data-copy-message="链接已复制" title="复制链接">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                </svg>
              </button>
            </div>
          </div>`;
        }).join('')
      : '<div class="parse-result-item"><label>链接:</label><span class="no-result">无</span></div>';

    return `
      <div class="email-item" data-message-id="${email.messageId}">
        <div class="email-header" data-toggle-email="${email.messageId}">
          <div class="email-from">${email.from || '未知发件人'}</div>
          <div class="email-time">${this.formatTime(email.receivedTime)}</div>
          <div class="expand-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="transform: rotate(180deg);">
              <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
            </svg>
          </div>
        </div>
        <div class="email-subject">${email.subject || '无主题'}</div>

        <!-- 解析结果区域 -->
        <div class="parse-results">
          ${codesHtml}
          ${linksHtml}
        </div>

        <!-- 邮件详情区域（默认展开） -->
        <div class="email-detail" style="display: block;">
          <div class="email-content">
            <h4>邮件全文：</h4>
            <div class="content-text">${email.content || '无内容'}</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 使用规则解析所有邮件
   */
  async parseEmailsWithRules() {
    if (this.emails.length === 0) {
      return;
    }

    // 确保规则已加载
    await this.loadRules();

    const allRules = [...this.localRules, ...this.sharedRules];
    console.log(`加载的规则数量: 本地=${this.localRules.length}, 共享=${this.sharedRules.length}, 总计=${allRules.length}`);

    // 为每封邮件进行解析
    for (const email of this.emails) {
      console.log(`\n=== 开始解析邮件 ===`);
      console.log(`发件人: ${email.from}`);
      console.log(`主题: ${email.subject}`);
      console.log(`邮件内容预览:`, email.content ? email.content.substring(0, 200) + '...' : '无内容');

      const parseResults = await emailParser.extractFromEmail(email, allRules);

      console.log(`解析结果统计:`);
      console.log(`- 验证码数量: ${parseResults.codes.length}`);
      console.log(`- 链接数量: ${parseResults.links.length}`);

      if (parseResults.codes.length > 0) {
        console.log(`验证码详情:`);
        parseResults.codes.forEach((code, index) => {
          console.log(`  ${index + 1}. 值: "${code.value}", 置信度: ${code.confidence}, 匹配类型: ${code.matchType || 'N/A'}, 来源: ${code.source}`);
        });
      }

      if (parseResults.links.length > 0) {
        console.log(`链接详情:`);
        parseResults.links.forEach((link, index) => {
          console.log(`  ${index + 1}. 值: "${link.value}", 置信度: ${link.confidence}, 来源: ${link.source}`);
        });
      }

      // 如果是GitHub邮件但没有解析结果，强制使用GitHub规则测试
      if (email.from && email.from.toLowerCase().includes('github') &&
          parseResults.codes.length === 0 && parseResults.links.length === 0) {
        console.log('GitHub邮件解析失败，尝试强制解析...');
        const githubRule = allRules.find(r => r.platform === 'GitHub');
        if (githubRule && email.content) {
          const codeMatch = email.content.match(/\b\d{6}\b/);
          if (codeMatch) {
            parseResults.codes.push({
              value: codeMatch[0],
              type: 'code',
              confidence: 0.9,
              source: 'GitHub强制解析'
            });
            console.log('强制解析成功，找到验证码:', codeMatch[0]);
          }
        }
      }

      email.parseResults = parseResults;
      console.log(`=== 邮件解析完成 ===\n`);
    }
  }

  /**
   * 加载解析规则
   */
  async loadRules() {
    try {
      // 加载本地规则
      this.localRules = await storageManager.loadParseRules();
      console.log('本地规则加载完成:', this.localRules);

      // 加载共享规则
      const response = await apiClient.getSharedRules();
      console.log('共享规则API响应:', response);
      if (response.success) {
        this.sharedRules = response.rules || [];
        console.log('共享规则加载完成:', this.sharedRules);
      } else {
        console.error('共享规则加载失败:', response.message);
        this.sharedRules = [];
      }
    } catch (error) {
      console.error('加载规则失败:', error);
      this.localRules = [];
      this.sharedRules = [];
    }
  }

  /**
   * 复制文本到剪贴板
   */
  async copyText(text, successMessage = '已复制') {
    try {
      await navigator.clipboard.writeText(text);
      this.showToast(successMessage);
    } catch (error) {
      console.error('复制失败:', error);
      this.showToast('复制失败');
    }
  }

  /**
   * 切换邮件详情显示
   */
  toggleEmailDetail(messageId) {
    const emailItem = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!emailItem) return;

    const detailDiv = emailItem.querySelector('.email-detail');
    const expandIcon = emailItem.querySelector('.expand-icon svg');

    if (detailDiv.style.display === 'none' || detailDiv.style.display === '') {
      // 展开
      detailDiv.style.display = 'block';
      expandIcon.style.transform = 'rotate(180deg)';
      emailItem.classList.add('expanded');
    } else {
      // 折叠
      detailDiv.style.display = 'none';
      expandIcon.style.transform = 'rotate(0deg)';
      emailItem.classList.remove('expanded');
    }
  }









  /**
   * 切换标签页
   */
  switchTab(tabName) {
    // 更新标签按钮状态
    this.tabBtns.forEach(btn => {
      btn.classList.toggle('active', btn.dataset.tab === tabName);
    });
    
    // 显示对应内容
    this.localRulesTab.style.display = tabName === 'local' ? 'block' : 'none';
    this.sharedRulesTab.style.display = tabName === 'shared' ? 'block' : 'none';
    this.createRuleTab.style.display = tabName === 'create' ? 'block' : 'none';
    
    // 加载对应数据
    if (tabName === 'shared') {
      this.loadSharedRules();
    }
  }

  /**
   * 加载共享规则
   */
  async loadSharedRules() {
    try {
      const response = await apiClient.getSharedRules();
      if (response.success) {
        this.sharedRules = response.rules || [];
        this.updateSharedRulesList();
      }
    } catch (error) {
      console.error('加载共享规则失败:', error);
    }
  }

  /**
   * 更新本地规则列表
   */
  updateLocalRulesList() {
    if (this.localRules.length === 0) {
      this.localRulesList.innerHTML = '<div class="empty-rules"><p>暂无本地规则</p><small>创建规则来自动提取验证码</small></div>';
      return;
    }
    
    this.localRulesList.innerHTML = this.localRules.map(rule => `
      <div class="rule-item">
        <div class="rule-sender">${rule.senderEmail}</div>
        <div class="rule-platform">${rule.platform}</div>
      </div>
    `).join('');
  }

  /**
   * 更新共享规则列表
   */
  updateSharedRulesList() {
    if (this.sharedRules.length === 0) {
      this.sharedRulesList.innerHTML = '<div class="empty-rules"><p>暂无共享规则</p><small>其他用户分享的规则将显示在这里</small></div>';
      return;
    }
    
    this.sharedRulesList.innerHTML = this.sharedRules.map(rule => `
      <div class="rule-item">
        <div class="rule-sender">${rule.senderEmail}</div>
        <div class="rule-platform">${rule.platform}</div>
      </div>
    `).join('');
  }

  /**
   * 处理规则表单提交
   */
  async handleRuleSubmit(event) {
    event.preventDefault();

    const formData = new FormData(this.ruleForm);
    const ruleData = {
      senderEmail: document.getElementById('senderEmail').value,
      platform: document.getElementById('platform').value,
      emailExample: document.getElementById('emailExample').value,
      codeExample: document.getElementById('codeExample').value
    };

    try {
      // 创建规则
      const rule = ruleManager.createRuleFromExample(ruleData);

      // 检查是保存到本地还是共享
      const isShared = event.submitter.classList.contains('save-shared-btn');

      let success = false;
      if (isShared) {
        success = await ruleManager.shareRule(rule);
        if (success) {
          this.showToast('规则已共享给所有用户');
          await this.loadSharedRules();
        }
      } else {
        success = await ruleManager.saveRuleLocally(rule);
        if (success) {
          this.showToast('规则已保存到本地');
          this.localRules = await storageManager.loadParseRules();
          this.updateLocalRulesList();
        }
      }

      if (success) {
        this.ruleForm.reset();
      } else {
        this.showToast('保存规则失败');
      }
    } catch (error) {
      console.error('创建规则失败:', error);
      this.showToast('创建规则失败: ' + error.message);
    }
  }

  /**
   * 显示加载状态
   */
  showLoading(show) {
    this.loadingState.style.display = show ? 'flex' : 'none';
    this.emailListContent.style.display = show ? 'none' : 'block';
    
    if (!show && this.emails.length === 0) {
      this.emptyState.style.display = 'flex';
    }
  }

  /**
   * 更新状态指示器
   */
  updateStatus(type, message) {
    this.statusText.textContent = message;
    this.statusDot.className = `status-dot ${type}`;
  }

  /**
   * 显示提示消息
   */
  showToast(message, type = 'info') {
    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;

    // 添加到页面
    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 3000);

    console.log('Toast:', message);
  }

  /**
   * 格式化时间
   */
  formatTime(timeString) {
    if (!timeString) return '';
    
    const date = new Date(timeString);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return date.toLocaleDateString();
    }
  }
}

// 初始化应用
const app = new SidepanelApp();

// 导出到全局作用域供HTML调用
window.app = app;
