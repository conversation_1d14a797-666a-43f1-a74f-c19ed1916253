<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时邮箱助手</title>
    <link rel="stylesheet" href="../assets/styles/common.css">
    <link rel="stylesheet" href="sidepanel.css">
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <header class="header">
            <div class="logo">
                <img src="../icons/icon.png" alt="临时邮箱助手" class="logo-icon">
                <h1 class="title">临时邮箱助手</h1>
            </div>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">就绪</span>
            </div>
        </header>

        <!-- 邮箱生成区域 -->
        <section class="email-generator">
            <div class="generator-card">
                <h2>生成临时邮箱</h2>
                <div class="email-display" id="emailDisplay">
                    <input type="text" id="generatedEmail" readonly placeholder="点击生成临时邮箱">
                    <button class="copy-btn" id="copyEmailBtn" title="复制邮箱地址">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                        </svg>
                    </button>
                </div>
                <div class="generator-actions">
                    <button class="generate-btn" id="generateBtn">生成新邮箱</button>
                    <button class="refresh-btn" id="refreshBtn" disabled>刷新邮件</button>
                </div>
                <div class="email-info" id="emailInfo">
                    <small>生成时间: <span id="generateTime">-</span></small>
                </div>
            </div>
        </section>

        <!-- 邮件列表区域 -->
        <section class="email-list">
            <div class="list-header">
                <h2>收到的邮件</h2>
                <div class="list-controls">
                    <button class="auto-refresh-btn" id="autoRefreshBtn" title="自动刷新">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                        </svg>
                    </button>
                    <span class="email-count" id="emailCount">0 封邮件</span>
                </div>
            </div>
            
            <div class="email-container" id="emailContainer">
                <div class="empty-state" id="emptyState">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" opacity="0.3">
                        <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                    </svg>
                    <p>暂无邮件</p>
                    <small>生成临时邮箱后，邮件将自动显示在这里</small>
                </div>
                
                <div class="loading-state" id="loadingState" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p>正在获取邮件...</p>
                </div>
                
                <div class="email-list-content" id="emailListContent"></div>
            </div>
        </section>



        <!-- 解析规则管理区域 -->
        <section class="rule-management">
            <div class="rule-card">
                <h2>解析规则管理</h2>
                <div class="rule-tabs">
                    <button class="tab-btn active" data-tab="local">本地规则</button>
                    <button class="tab-btn" data-tab="shared">共享规则</button>
                    <button class="tab-btn" data-tab="create">创建规则</button>
                </div>
                
                <div class="tab-content" id="localRulesTab">
                    <div class="rule-list" id="localRulesList">
                        <div class="empty-rules">
                            <p>暂无本地规则</p>
                            <small>创建规则来自动提取验证码</small>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="sharedRulesTab" style="display: none;">
                    <div class="rule-list" id="sharedRulesList">
                        <div class="loading-rules">
                            <div class="loading-spinner"></div>
                            <p>加载共享规则中...</p>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="createRuleTab" style="display: none;">
                    <form class="rule-form" id="ruleForm">
                        <div class="form-group">
                            <label for="senderEmail">发件人邮箱:</label>
                            <input type="email" id="senderEmail" placeholder="例如: <EMAIL>" required>
                        </div>
                        <div class="form-group">
                            <label for="platform">平台名称:</label>
                            <input type="text" id="platform" placeholder="例如: GitHub" required>
                        </div>
                        <div class="form-group">
                            <label for="emailExample">邮件示例:</label>
                            <textarea id="emailExample" placeholder="粘贴完整的邮件内容..." rows="4" required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="codeExample">验证码示例:</label>
                            <input type="text" id="codeExample" placeholder="例如: 123456" required>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="save-local-btn">保存到本地</button>
                            <button type="button" class="save-shared-btn">共享给所有用户</button>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </div>

    <!-- 加载模块 -->
    <script type="module" src="sidepanel.js"></script>
</body>
</html>
