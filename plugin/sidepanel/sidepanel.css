/* 临时邮箱助手 - 侧边栏样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #f8f9fa;
    overflow-x: hidden;
}

.container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    padding: 0;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
}

.title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #4ade80;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-dot.error {
    background-color: #ef4444;
}

.status-dot.warning {
    background-color: #f59e0b;
}

/* 卡片通用样式 */
.generator-card,
.extraction-card,
.rule-card {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin: 16px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e5e7eb;
}

.generator-card h2,
.extraction-card h2,
.rule-card h2 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #374151;
}

/* 邮箱生成区域 */
.email-display {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

#generatedEmail {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 13px;
    background-color: #f9fafb;
    color: #374151;
}

.copy-btn {
    padding: 8px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s;
}

.copy-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.copy-btn:active {
    background-color: #e5e7eb;
}

.generator-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.generate-btn,
.refresh-btn {
    flex: 1;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.generate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.generate-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.refresh-btn {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.refresh-btn:enabled:hover {
    background: #e5e7eb;
    color: #374151;
}

.refresh-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.email-info {
    color: #6b7280;
    font-size: 12px;
}

/* 邮件列表区域 */
.email-list {
    margin: 16px;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.list-header h2 {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
}

.list-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.auto-refresh-btn {
    padding: 6px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s;
}

.auto-refresh-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.auto-refresh-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.email-count {
    font-size: 12px;
    color: #6b7280;
}

.email-container {
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    min-height: 200px;
}

.empty-state,
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: #6b7280;
}

.empty-state p,
.loading-state p {
    margin: 8px 0 4px;
    font-weight: 500;
}

.empty-state small,
.loading-state small {
    font-size: 12px;
    opacity: 0.8;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 邮件项样式 */
.email-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: background-color 0.2s;
}

.email-item:hover {
    background-color: #f9fafb;
}

.email-item:last-child {
    border-bottom: none;
}

.email-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 4px;
}

.email-from {
    font-weight: 500;
    color: #374151;
    font-size: 13px;
}

.email-time {
    font-size: 11px;
    color: #9ca3af;
}

.email-subject {
    font-size: 13px;
    color: #6b7280;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.email-preview {
    font-size: 11px;
    color: #9ca3af;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 验证码提取区域 */
.extracted-codes {
    min-height: 60px;
}

.code-item,
.link-item {
    margin-bottom: 12px;
}

.code-item:last-child,
.link-item:last-child {
    margin-bottom: 0;
}

.code-item label,
.link-item label {
    display: block;
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 4px;
}

.code-display,
.link-display {
    display: flex;
    gap: 8px;
}

.code-display input,
.link-display input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 13px;
    background-color: #f9fafb;
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #059669;
}

.no-extraction {
    text-align: center;
    padding: 20px;
    color: #6b7280;
}

.no-extraction p {
    margin-bottom: 4px;
}

.no-extraction small {
    font-size: 12px;
    opacity: 0.8;
}

/* 规则管理区域 */
.rule-tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 16px;
}

.tab-btn {
    flex: 1;
    padding: 8px 12px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 13px;
    color: #6b7280;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
}

.tab-btn:hover {
    color: #374151;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.tab-content {
    min-height: 120px;
}

.rule-list {
    max-height: 200px;
    overflow-y: auto;
}

.empty-rules,
.loading-rules {
    text-align: center;
    padding: 20px;
    color: #6b7280;
}

.rule-item {
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    margin-bottom: 8px;
    background: #f9fafb;
}

.rule-item:last-child {
    margin-bottom: 0;
}

.rule-sender {
    font-size: 13px;
    font-weight: 500;
    color: #374151;
}

.rule-platform {
    font-size: 12px;
    color: #6b7280;
}

/* 表单样式 */
.rule-form {
    max-height: 300px;
    overflow-y: auto;
}

.form-group {
    margin-bottom: 12px;
}

.form-group label {
    display: block;
    font-size: 12px;
    color: #374151;
    margin-bottom: 4px;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 13px;
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.save-local-btn,
.save-shared-btn {
    flex: 1;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.save-local-btn {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.save-local-btn:hover {
    background: #e5e7eb;
}

.save-shared-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.save-shared-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 响应式设计 */
@media (max-width: 320px) {
    .container {
        max-width: 100%;
    }
    
    .generator-card,
    .extraction-card,
    .rule-card {
        margin: 8px;
        padding: 12px;
    }
    
    .email-list {
        margin: 8px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 邮件解析结果样式 */
.email-item {
    cursor: default;
}

.email-header {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    transition: background-color 0.2s ease;
}

.email-header:hover {
    background-color: #f8f9fa;
    border-radius: 4px;
}

.expand-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    opacity: 0.6;
    transition: all 0.2s ease;
}

.expand-icon svg {
    transition: transform 0.2s ease;
}

.email-header:hover .expand-icon {
    opacity: 1;
}

.parse-results {
    margin-top: 12px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.parse-result-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.parse-result-item:last-child {
    margin-bottom: 0;
}

.parse-result-item label {
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
    min-width: 50px;
    flex-shrink: 0;
}

.result-display {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
}

.result-display input {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 12px;
    background-color: white;
    color: #495057;
}

.result-display .copy-btn {
    padding: 6px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
    min-width: 28px;
    height: 28px;
}

.result-display .copy-btn:hover {
    background-color: #0056b3;
}

.no-result {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

.email-detail {
    margin-top: 12px;
    padding: 12px;
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    animation: slideDown 0.2s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        padding-top: 0;
        padding-bottom: 0;
    }
    to {
        opacity: 1;
        max-height: 500px;
        padding-top: 12px;
        padding-bottom: 12px;
    }
}

.email-detail h4 {
    font-size: 13px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.content-text {
    font-size: 11px;
    line-height: 1.3;
    color: #6c757d;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 200px;
    overflow-y: auto;
    padding: 6px 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.email-item.expanded {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
