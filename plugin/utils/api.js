/**
 * API通信工具模块
 * 负责与后端服务器的所有通信
 */

class ApiClient {
    constructor() {
        this.baseUrl = 'http://1.12.224.176:8080';
        this.apiKey = 'mailplugin_api_key_2024';
        this.timeout = 10000; // 10秒超时
    }

    /**
     * 发送HTTP请求的通用方法
     * @param {string} endpoint - API端点
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 响应数据
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': this.apiKey,
                ...options.headers
            },
            ...options
        };

        // 添加请求体
        if (options.data) {
            config.body = JSON.stringify(options.data);
        }

        try {
            console.log(`[API] 发送请求: ${config.method} ${url}`);
            
            // 创建超时Promise
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('请求超时')), this.timeout);
            });

            // 发送请求
            const fetchPromise = fetch(url, config);
            const response = await Promise.race([fetchPromise, timeoutPromise]);

            console.log(`[API] 响应状态: ${response.status}`);

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            console.log(`[API] 响应数据:`, data);
            
            return data;
        } catch (error) {
            console.error(`[API] 请求失败:`, error);
            throw error;
        }
    }

    /**
     * 生成临时邮箱
     * @param {string} tempEmail - 临时邮箱地址
     * @param {string} generateTime - 生成时间
     * @param {string} userId - 用户ID
     * @returns {Promise<Object>} 生成结果
     */
    async generateEmail(tempEmail, generateTime, userId) {
        return await this.request('/api/generate-email', {
            method: 'POST',
            data: {
                tempEmail: tempEmail,
                generateTime: generateTime,
                userId: userId,
                apiKey: this.apiKey
            }
        });
    }

    /**
     * 获取邮件列表
     * @param {string} userId - 用户ID
     * @returns {Promise<Array>} 邮件列表
     */
    async getEmails(userId) {
        return await this.request(`/api/get-emails?userId=${encodeURIComponent(userId)}`);
    }

    /**
     * 获取共享解析规则
     * @returns {Promise<Array>} 解析规则列表
     */
    async getSharedRules() {
        return await this.request('/api/get-shared-rules');
    }

    /**
     * 上传共享解析规则
     * @param {Object} rule - 解析规则对象
     * @returns {Promise<Object>} 上传结果
     */
    async uploadSharedRule(rule) {
        return await this.request('/api/upload-shared-rule', {
            method: 'POST',
            data: rule
        });
    }

    /**
     * 健康检查
     * @returns {Promise<Object>} 健康状态
     */
    async healthCheck() {
        return await this.request('/api/health');
    }

    /**
     * 获取系统状态
     * @returns {Promise<Object>} 系统状态
     */
    async getStatus() {
        return await this.request('/api/status');
    }

    /**
     * 测试连接
     * @returns {Promise<boolean>} 连接是否成功
     */
    async testConnection() {
        try {
            await this.healthCheck();
            return true;
        } catch (error) {
            console.error('[API] 连接测试失败:', error);
            return false;
        }
    }

    /**
     * 重试机制包装器
     * @param {Function} apiCall - API调用函数
     * @param {number} maxRetries - 最大重试次数
     * @param {number} delay - 重试延迟(毫秒)
     * @returns {Promise<any>} API调用结果
     */
    async withRetry(apiCall, maxRetries = 3, delay = 1000) {
        let lastError;
        
        for (let i = 0; i <= maxRetries; i++) {
            try {
                return await apiCall();
            } catch (error) {
                lastError = error;
                
                if (i === maxRetries) {
                    break;
                }
                
                console.log(`[API] 重试 ${i + 1}/${maxRetries}, 延迟 ${delay}ms`);
                await new Promise(resolve => setTimeout(resolve, delay));
                delay *= 2; // 指数退避
            }
        }
        
        throw lastError;
    }

    /**
     * 批量获取邮件（带重试）
     * @param {string} userId - 用户ID
     * @returns {Promise<Array>} 邮件列表
     */
    async getEmailsWithRetry(userId) {
        return await this.withRetry(() => this.getEmails(userId));
    }

    /**
     * 生成用户ID
     * @returns {string} 唯一用户ID
     */
    generateUserId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        return `user_${timestamp}_${random}`;
    }

    /**
     * 生成随机邮箱前缀
     * @param {number} minLength - 最小长度
     * @param {number} maxLength - 最大长度
     * @returns {string} 随机字符串
     */
    generateRandomPrefix(minLength = 8, maxLength = 12) {
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        const length = Math.floor(Math.random() * (maxLength - minLength + 1)) + minLength;
        let result = '';
        
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        
        return result;
    }

    /**
     * 格式化时间
     * @param {Date} date - 日期对象
     * @returns {string} 格式化的时间字符串
     */
    formatTime(date = new Date()) {
        return date.toISOString().replace('T', ' ').substring(0, 19);
    }

    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * 获取错误信息
     * @param {Error} error - 错误对象
     * @returns {string} 用户友好的错误信息
     */
    getErrorMessage(error) {
        if (error.message.includes('请求超时')) {
            return '网络连接超时，请检查网络状态';
        } else if (error.message.includes('HTTP错误')) {
            return '服务器响应错误，请稍后重试';
        } else if (error.message.includes('Failed to fetch')) {
            return '无法连接到服务器，请检查网络连接';
        } else {
            return error.message || '未知错误';
        }
    }
}

// 创建全局API客户端实例
const apiClient = new ApiClient();

// 导出API客户端（用于模块化环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiClient;
}

// 全局可用（用于浏览器环境）
window.ApiClient = ApiClient;
window.apiClient = apiClient;
