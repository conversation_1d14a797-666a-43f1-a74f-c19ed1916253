/**
 * 邮件解析模块
 * 负责从邮件内容中提取验证码和链接
 */

/**
 * 邮件解析器类
 */
export class EmailParser {
  constructor() {
    // 预定义的通用验证码模式
    this.defaultPatterns = [
      {
        name: '6位数字验证码',
        regex: '\\b\\d{6}\\b',
        type: 'CODE',
        priority: 10
      },
      {
        name: '4位数字验证码',
        regex: '\\b\\d{4}\\b',
        type: 'CODE',
        priority: 8
      },
      {
        name: '8位数字验证码',
        regex: '\\b\\d{8}\\b',
        type: 'CODE',
        priority: 9
      },
      {
        name: '6位字母数字验证码',
        regex: '\\b[A-Za-z0-9]{6}\\b',
        type: 'CODE',
        priority: 7
      },
      {
        name: 'HTTP链接',
        regex: 'https?://[^\\s<>"{}|\\\\^`\\[\\]]+',
        type: 'LINK',
        priority: 9
      },
      {
        name: '邮箱地址',
        regex: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}',
        type: 'EMAIL',
        priority: 5
      }
    ];

    // 验证码相关关键词
    this.codeKeywords = [
      '验证码', 'verification code', 'code', '动态码', 'otp',
      '安全码', 'security code', '确认码', 'confirmation code',
      '激活码', 'activation code', '登录码', 'login code'
    ];

    // 链接相关关键词
    this.linkKeywords = [
      '点击', 'click', '访问', 'visit', '链接', 'link',
      '激活', 'activate', '确认', 'confirm', '验证', 'verify'
    ];
  }

  /**
   * 从邮件中提取验证码和链接
   * @param {Object} email 邮件对象
   * @param {Array} customRules 自定义规则数组
   * @returns {Object} 提取结果
   */
  async extractFromEmail(email, customRules = []) {
    if (!email || !email.content) {
      return { codes: [], links: [], emails: [] };
    }

    const content = this.preprocessContent(email.content);
    const sender = email.from || '';

    // 查找匹配的自定义规则
    const matchedRule = await this.findMatchingRule(sender, customRules);

    let results = { codes: [], links: [], emails: [] };

    if (matchedRule) {
      // 优先使用匹配的自定义规则提取
      console.log(`使用匹配规则解析邮件: ${matchedRule.platform} - ${matchedRule.senderEmail}`);
      results = this.extractWithRule(content, matchedRule, email);
    } else {
      // 没有匹配规则时，使用默认模式但要更严格
      console.log(`未找到匹配规则，使用默认模式解析: ${sender}`);
      results = this.extractWithDefaultPatterns(content, email);
    }

    // 后处理和验证结果，确保每种类型只返回最佳结果
    results = this.postProcessResults(results, content, matchedRule ? true : false);

    return results;
  }

  /**
   * 预处理邮件内容
   * @param {string} content 原始内容
   * @returns {string} 处理后的内容
   */
  preprocessContent(content) {
    if (!content) return '';
    
    // 移除HTML标签
    let text = content.replace(/<[^>]*>/g, ' ');
    
    // 解码HTML实体
    text = text.replace(/&nbsp;/g, ' ')
               .replace(/&lt;/g, '<')
               .replace(/&gt;/g, '>')
               .replace(/&amp;/g, '&')
               .replace(/&quot;/g, '"')
               .replace(/&#39;/g, "'");
    
    // 标准化空白字符
    text = text.replace(/\s+/g, ' ').trim();
    
    return text;
  }

  /**
   * 查找匹配的自定义规则
   * @param {string} sender 发件人
   * @param {Array} rules 规则数组
   * @returns {Object|null} 匹配的规则
   */
  async findMatchingRule(sender, rules) {
    if (!sender || !rules.length) {
      console.log('规则匹配失败: 发件人为空或规则列表为空', { sender, rulesCount: rules.length });
      return null;
    }

    console.log('正在匹配规则，发件人:', sender);
    console.log('可用规则数量:', rules.length);

    // 创建规则管理器实例用于匹配计算
    const { RuleManager } = await import('./rule-manager.js');
    const ruleManager = new RuleManager();

    let bestMatch = null;
    let bestConfidence = 0;

    for (const rule of rules) {
      if (!rule.senderEmail) {
        console.log('跳过规则（无发件人邮箱）:', rule);
        continue;
      }

      // 检查规则是否激活（兼容 isActive 和 isShared 字段）
      const isActive = rule.isActive !== undefined ? rule.isActive : rule.isShared;
      if (!isActive) {
        console.log('跳过规则（未激活）:', rule);
        continue;
      }

      // 使用新的匹配算法计算置信度
      const matchResult = ruleManager.calculateMatchConfidence(sender, rule);

      console.log(`规则匹配检查: ${rule.senderEmail} vs ${sender}`);
      console.log(`匹配结果: ${matchResult.matchType}, 置信度: ${matchResult.confidence}`);

      if (matchResult.isMatch && matchResult.confidence > bestConfidence) {
        bestMatch = rule;
        bestConfidence = matchResult.confidence;

        // 添加匹配信息到规则对象
        bestMatch._matchInfo = {
          confidence: matchResult.confidence,
          matchType: matchResult.matchType
        };
      }
    }

    if (bestMatch) {
      console.log('找到最佳匹配规则:', {
        platform: bestMatch.platform,
        senderEmail: bestMatch.senderEmail,
        confidence: bestMatch._matchInfo.confidence,
        matchType: bestMatch._matchInfo.matchType
      });
    } else {
      console.log('未找到匹配规则');
    }

    return bestMatch;
  }

  /**
   * 使用自定义规则提取
   * @param {string} content 邮件内容
   * @param {Object} rule 规则对象
   * @param {Object} email 邮件对象
   * @returns {Object} 提取结果
   */
  extractWithRule(content, rule, email) {
    const results = { codes: [], links: [], emails: [] };

    try {
      const regex = new RegExp(rule.pattern, 'gi');
      const matches = content.match(regex);

      if (matches) {
        const uniqueMatches = [...new Set(matches)];

        uniqueMatches.forEach(match => {
          // 基础置信度从规则匹配信息获取
          const baseConfidence = rule._matchInfo ? rule._matchInfo.confidence : 0.9;

          const result = {
            value: match.trim(),
            source: 'custom_rule',
            rule: rule.platform,
            confidence: Math.min(baseConfidence + 0.1, 1.0), // 规则匹配成功后提升置信度
            matchType: rule._matchInfo ? rule._matchInfo.matchType : 'unknown',
            position: content.indexOf(match),
            context: this.getContext(content, match)
          };

          if (rule.patternType === 'CODE' || rule.patternType === 'NUMERIC_CODE' || rule.patternType === 'ALPHANUMERIC_CODE') {
            results.codes.push(result);
          } else if (rule.patternType === 'LINK') {
            results.links.push(result);
          }
        });
      }
    } catch (error) {
      console.error('自定义规则解析失败:', error);
    }

    return results;
  }

  /**
   * 使用默认模式提取
   * @param {string} content 邮件内容
   * @param {Object} email 邮件对象
   * @returns {Object} 提取结果
   */
  extractWithDefaultPatterns(content, email) {
    const results = { codes: [], links: [], emails: [] };
    
    this.defaultPatterns.forEach(pattern => {
      try {
        const regex = new RegExp(pattern.regex, 'gi');
        const matches = content.match(regex);
        
        if (matches) {
          const uniqueMatches = [...new Set(matches)];
          
          uniqueMatches.forEach(match => {
            const result = {
              value: match.trim(),
              source: 'default_pattern',
              pattern: pattern.name,
              confidence: this.calculateConfidence(match, content, pattern),
              position: content.indexOf(match),
              context: this.getContext(content, match)
            };
            
            if (pattern.type === 'CODE') {
              results.codes.push(result);
            } else if (pattern.type === 'LINK') {
              results.links.push(result);
            } else if (pattern.type === 'EMAIL') {
              results.emails.push(result);
            }
          });
        }
      } catch (error) {
        console.error('默认模式解析失败:', pattern.name, error);
      }
    });
    
    return results;
  }

  /**
   * 计算匹配置信度
   * @param {string} match 匹配的文本
   * @param {string} content 完整内容
   * @param {Object} pattern 模式对象
   * @returns {number} 置信度 (0-1)
   */
  calculateConfidence(match, content, pattern) {
    let confidence = pattern.priority / 10;
    
    // 检查周围是否有相关关键词
    const context = this.getContext(content, match, 50);
    const lowerContext = context.toLowerCase();
    
    if (pattern.type === 'CODE') {
      // 验证码相关关键词加分
      const hasCodeKeyword = this.codeKeywords.some(keyword => 
        lowerContext.includes(keyword.toLowerCase())
      );
      if (hasCodeKeyword) confidence += 0.2;
      
      // 数字验证码在特定长度下加分
      if (/^\d+$/.test(match)) {
        if (match.length === 6) confidence += 0.1;
        if (match.length === 4) confidence += 0.05;
      }
    } else if (pattern.type === 'LINK') {
      // 链接相关关键词加分
      const hasLinkKeyword = this.linkKeywords.some(keyword => 
        lowerContext.includes(keyword.toLowerCase())
      );
      if (hasLinkKeyword) confidence += 0.2;
      
      // HTTPS链接加分
      if (match.startsWith('https://')) confidence += 0.1;
    }
    
    return Math.min(confidence, 1);
  }

  /**
   * 获取匹配文本的上下文
   * @param {string} content 完整内容
   * @param {string} match 匹配的文本
   * @param {number} radius 上下文半径
   * @returns {string} 上下文文本
   */
  getContext(content, match, radius = 30) {
    const index = content.indexOf(match);
    if (index === -1) return match;
    
    const start = Math.max(0, index - radius);
    const end = Math.min(content.length, index + match.length + radius);
    
    return content.substring(start, end);
  }

  /**
   * 后处理提取结果
   * @param {Object} results 原始结果
   * @param {string} content 邮件内容
   * @param {boolean} hasMatchedRule 是否有匹配的规则
   * @returns {Object} 处理后的结果
   */
  postProcessResults(results, content, hasMatchedRule = false) {
    // 按置信度排序
    results.codes.sort((a, b) => b.confidence - a.confidence);
    results.links.sort((a, b) => b.confidence - a.confidence);
    results.emails.sort((a, b) => b.confidence - a.confidence);

    // 去重和过滤
    results.codes = this.deduplicateResults(results.codes);
    results.links = this.deduplicateResults(results.links);
    results.emails = this.deduplicateResults(results.emails);

    if (hasMatchedRule) {
      // 有匹配规则时，使用较低的置信度阈值，但只返回最佳结果
      results.codes = results.codes.filter(r => r.confidence >= 0.5).slice(0, 1);
      results.links = results.links.filter(r => r.confidence >= 0.5).slice(0, 1);
    } else {
      // 没有匹配规则时，使用更高的置信度阈值
      results.codes = results.codes.filter(r => r.confidence >= 0.7).slice(0, 1);
      results.links = results.links.filter(r => r.confidence >= 0.7).slice(0, 1);
    }

    // 邮件地址限制
    results.emails = results.emails.filter(r => r.confidence >= 0.3).slice(0, 1);

    return results;
  }

  /**
   * 去重结果
   * @param {Array} results 结果数组
   * @returns {Array} 去重后的结果
   */
  deduplicateResults(results) {
    const seen = new Set();
    return results.filter(result => {
      const key = result.value.toLowerCase();
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * 验证提取结果的有效性
   * @param {string} value 提取的值
   * @param {string} type 类型
   * @returns {boolean} 是否有效
   */
  validateResult(value, type) {
    if (!value || typeof value !== 'string') return false;
    
    switch (type) {
      case 'CODE':
        // 验证码应该是4-12位的字母数字组合
        return /^[A-Za-z0-9]{4,12}$/.test(value);
      
      case 'LINK':
        // 验证URL格式
        try {
          new URL(value);
          return true;
        } catch {
          return false;
        }
      
      case 'EMAIL':
        // 验证邮箱格式
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      
      default:
        return true;
    }
  }

  /**
   * 获取提取统计信息
   * @param {Object} results 提取结果
   * @returns {Object} 统计信息
   */
  getExtractionStats(results) {
    return {
      totalCodes: results.codes.length,
      totalLinks: results.links.length,
      totalEmails: results.emails.length,
      highConfidenceCodes: results.codes.filter(r => r.confidence >= 0.8).length,
      highConfidenceLinks: results.links.filter(r => r.confidence >= 0.8).length,
      customRuleMatches: results.codes.filter(r => r.source === 'custom_rule').length +
                        results.links.filter(r => r.source === 'custom_rule').length,
      defaultPatternMatches: results.codes.filter(r => r.source === 'default_pattern').length +
                           results.links.filter(r => r.source === 'default_pattern').length
    };
  }
}

// 创建单例实例
export const emailParser = new EmailParser();

// 默认导出
export default emailParser;
