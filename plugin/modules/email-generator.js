/**
 * 邮箱生成模块
 * 负责生成临时邮箱地址和用户ID
 */

// 配置常量
const CONFIG = {
  DOMAIN: 'chaiyuangungunlai.dpdns.org',
  MIN_LENGTH: 8,
  MAX_LENGTH: 12,
  CHARSET: 'abcdefghijklmnopqrstuvwxyz0123456789'
};

/**
 * 邮箱生成器类
 */
export class EmailGenerator {
  constructor() {
    this.currentSession = null;
  }

  /**
   * 生成随机字符串
   * @param {number} length 字符串长度
   * @returns {string} 随机字符串
   */
  generateRandomString(length) {
    let result = '';
    const charactersLength = CONFIG.CHARSET.length;
    
    for (let i = 0; i < length; i++) {
      result += CONFIG.CHARSET.charAt(Math.floor(Math.random() * charactersLength));
    }
    
    return result;
  }

  /**
   * 生成用户ID
   * @returns {string} 用户ID (时间戳 + 随机数)
   */
  generateUserId() {
    const timestamp = Date.now();
    const randomNum = Math.floor(Math.random() * 10000);
    return `${timestamp}_${randomNum}`;
  }

  /**
   * 生成临时邮箱地址
   * @returns {Object} 包含邮箱信息的对象
   */
  generateTempEmail() {
    // 生成随机长度（8-12位）
    const length = Math.floor(Math.random() * (CONFIG.MAX_LENGTH - CONFIG.MIN_LENGTH + 1)) + CONFIG.MIN_LENGTH;
    
    // 生成随机字符串
    const randomString = this.generateRandomString(length);
    
    // 拼接完整邮箱地址
    const tempEmail = `${randomString}@${CONFIG.DOMAIN}`;
    
    // 生成用户ID
    const userId = this.generateUserId();
    
    // 记录生成时间 (格式: yyyy-MM-dd HH:mm:ss)
    const generateTime = new Date().toISOString().replace('T', ' ').substring(0, 19);

    // 创建会话信息
    this.currentSession = {
      userId,
      tempEmail,
      generateTime,
      timestamp: Date.now(),
      isActive: true
    };

    return this.currentSession;
  }

  /**
   * 获取当前会话信息
   * @returns {Object|null} 当前会话信息
   */
  getCurrentSession() {
    return this.currentSession;
  }

  /**
   * 清除当前会话
   */
  clearSession() {
    this.currentSession = null;
  }

  /**
   * 验证邮箱地址格式
   * @param {string} email 邮箱地址
   * @returns {boolean} 是否为有效的临时邮箱地址
   */
  isValidTempEmail(email) {
    if (!email || typeof email !== 'string') {
      return false;
    }

    // 检查是否以指定域名结尾
    if (!email.endsWith(`@${CONFIG.DOMAIN}`)) {
      return false;
    }

    // 提取用户名部分
    const username = email.split('@')[0];
    
    // 检查用户名长度
    if (username.length < CONFIG.MIN_LENGTH || username.length > CONFIG.MAX_LENGTH) {
      return false;
    }

    // 检查用户名字符
    const validChars = new RegExp(`^[${CONFIG.CHARSET}]+$`);
    return validChars.test(username);
  }

  /**
   * 从存储中恢复会话
   * @param {Object} sessionData 会话数据
   * @returns {boolean} 是否成功恢复
   */
  restoreSession(sessionData) {
    if (!sessionData || !sessionData.userId || !sessionData.tempEmail) {
      return false;
    }

    // 验证邮箱格式
    if (!this.isValidTempEmail(sessionData.tempEmail)) {
      return false;
    }

    // 检查会话是否过期（24小时）
    const now = Date.now();
    const sessionAge = now - (sessionData.timestamp || 0);
    const maxAge = 24 * 60 * 60 * 1000; // 24小时

    if (sessionAge > maxAge) {
      console.log('会话已过期，无法恢复');
      return false;
    }

    this.currentSession = {
      ...sessionData,
      isActive: true
    };

    return true;
  }

  /**
   * 获取会话统计信息
   * @returns {Object} 统计信息
   */
  getSessionStats() {
    if (!this.currentSession) {
      return {
        hasSession: false,
        duration: 0,
        isExpired: false
      };
    }

    const now = Date.now();
    const duration = now - this.currentSession.timestamp;
    const maxAge = 24 * 60 * 60 * 1000; // 24小时
    const isExpired = duration > maxAge;

    return {
      hasSession: true,
      duration,
      isExpired,
      durationText: this.formatDuration(duration),
      email: this.currentSession.tempEmail,
      userId: this.currentSession.userId
    };
  }

  /**
   * 格式化持续时间
   * @param {number} duration 持续时间（毫秒）
   * @returns {string} 格式化的时间字符串
   */
  formatDuration(duration) {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 生成邮箱预览（用于测试）
   * @param {number} count 生成数量
   * @returns {Array} 邮箱地址数组
   */
  generatePreview(count = 5) {
    const previews = [];
    
    for (let i = 0; i < count; i++) {
      const length = Math.floor(Math.random() * (CONFIG.MAX_LENGTH - CONFIG.MIN_LENGTH + 1)) + CONFIG.MIN_LENGTH;
      const randomString = this.generateRandomString(length);
      previews.push(`${randomString}@${CONFIG.DOMAIN}`);
    }
    
    return previews;
  }
}

// 创建单例实例
export const emailGenerator = new EmailGenerator();

// 默认导出
export default emailGenerator;
