/**
 * 存储管理模块
 * 负责本地数据存储和缓存管理
 */

/**
 * 存储管理器类
 */
export class StorageManager {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = new Map();
    this.defaultExpiry = 5 * 60 * 1000; // 5分钟默认过期时间
  }

  /**
   * 保存数据到Chrome存储
   * @param {string} key 存储键
   * @param {*} value 存储值
   * @param {string} area 存储区域 ('local' | 'sync')
   * @returns {Promise<boolean>} 是否成功
   */
  async save(key, value, area = 'local') {
    try {
      const data = { [key]: value };
      
      if (area === 'sync') {
        await chrome.storage.sync.set(data);
      } else {
        await chrome.storage.local.set(data);
      }
      
      // 更新内存缓存
      this.cache.set(key, value);
      this.cacheExpiry.set(key, Date.now() + this.defaultExpiry);
      
      console.log(`数据已保存: ${key}`, value);
      return true;
    } catch (error) {
      console.error(`保存数据失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 从Chrome存储读取数据
   * @param {string} key 存储键
   * @param {*} defaultValue 默认值
   * @param {string} area 存储区域 ('local' | 'sync')
   * @returns {Promise<*>} 存储的值
   */
  async load(key, defaultValue = null, area = 'local') {
    try {
      // 先检查内存缓存
      if (this.cache.has(key)) {
        const expiry = this.cacheExpiry.get(key);
        if (expiry && Date.now() < expiry) {
          return this.cache.get(key);
        } else {
          // 缓存过期，清除
          this.cache.delete(key);
          this.cacheExpiry.delete(key);
        }
      }

      // 从Chrome存储读取
      let result;
      if (area === 'sync') {
        result = await chrome.storage.sync.get([key]);
      } else {
        result = await chrome.storage.local.get([key]);
      }
      
      const value = result[key] !== undefined ? result[key] : defaultValue;
      
      // 更新内存缓存
      if (value !== null) {
        this.cache.set(key, value);
        this.cacheExpiry.set(key, Date.now() + this.defaultExpiry);
      }
      
      return value;
    } catch (error) {
      console.error(`读取数据失败: ${key}`, error);
      return defaultValue;
    }
  }

  /**
   * 删除存储数据
   * @param {string} key 存储键
   * @param {string} area 存储区域 ('local' | 'sync')
   * @returns {Promise<boolean>} 是否成功
   */
  async remove(key, area = 'local') {
    try {
      if (area === 'sync') {
        await chrome.storage.sync.remove([key]);
      } else {
        await chrome.storage.local.remove([key]);
      }
      
      // 清除内存缓存
      this.cache.delete(key);
      this.cacheExpiry.delete(key);
      
      console.log(`数据已删除: ${key}`);
      return true;
    } catch (error) {
      console.error(`删除数据失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 清空所有存储数据
   * @param {string} area 存储区域 ('local' | 'sync')
   * @returns {Promise<boolean>} 是否成功
   */
  async clear(area = 'local') {
    try {
      if (area === 'sync') {
        await chrome.storage.sync.clear();
      } else {
        await chrome.storage.local.clear();
      }
      
      // 清空内存缓存
      this.cache.clear();
      this.cacheExpiry.clear();
      
      console.log(`${area}存储已清空`);
      return true;
    } catch (error) {
      console.error(`清空存储失败: ${area}`, error);
      return false;
    }
  }

  /**
   * 保存用户会话
   * @param {Object} sessionData 会话数据
   * @returns {Promise<boolean>} 是否成功
   */
  async saveUserSession(sessionData) {
    return await this.save('currentSession', sessionData);
  }

  /**
   * 加载用户会话
   * @returns {Promise<Object|null>} 会话数据
   */
  async loadUserSession() {
    return await this.load('currentSession');
  }

  /**
   * 清除用户会话
   * @returns {Promise<boolean>} 是否成功
   */
  async clearUserSession() {
    return await this.remove('currentSession');
  }

  /**
   * 保存解析规则
   * @param {Array} rules 规则数组
   * @returns {Promise<boolean>} 是否成功
   */
  async saveParseRules(rules) {
    return await this.save('parseRules', rules);
  }

  /**
   * 加载解析规则
   * @returns {Promise<Array>} 规则数组
   */
  async loadParseRules() {
    return await this.load('parseRules', []);
  }

  /**
   * 添加解析规则
   * @param {Object} rule 规则对象
   * @returns {Promise<boolean>} 是否成功
   */
  async addParseRule(rule) {
    try {
      const rules = await this.loadParseRules();
      
      // 检查是否已存在相同的规则
      const existingIndex = rules.findIndex(r => r.senderEmail === rule.senderEmail);
      
      if (existingIndex >= 0) {
        // 更新现有规则
        rules[existingIndex] = { ...rule, updatedAt: new Date().toISOString() };
      } else {
        // 添加新规则
        rules.push({ ...rule, createdAt: new Date().toISOString() });
      }
      
      return await this.saveParseRules(rules);
    } catch (error) {
      console.error('添加解析规则失败:', error);
      return false;
    }
  }

  /**
   * 删除解析规则
   * @param {string} senderEmail 发件人邮箱
   * @returns {Promise<boolean>} 是否成功
   */
  async removeParseRule(senderEmail) {
    try {
      const rules = await this.loadParseRules();
      const filteredRules = rules.filter(rule => rule.senderEmail !== senderEmail);
      return await this.saveParseRules(filteredRules);
    } catch (error) {
      console.error('删除解析规则失败:', error);
      return false;
    }
  }

  /**
   * 缓存邮件数据
   * @param {string} userId 用户ID
   * @param {Array} emails 邮件数组
   * @param {number} expiry 过期时间（毫秒）
   * @returns {Promise<boolean>} 是否成功
   */
  async cacheEmails(userId, emails, expiry = 60000) { // 默认1分钟过期
    const cacheKey = `emails_${userId}`;
    const cacheData = {
      emails,
      timestamp: Date.now(),
      expiry: Date.now() + expiry
    };
    
    return await this.save(cacheKey, cacheData);
  }

  /**
   * 获取缓存的邮件数据
   * @param {string} userId 用户ID
   * @returns {Promise<Array|null>} 邮件数组或null
   */
  async getCachedEmails(userId) {
    const cacheKey = `emails_${userId}`;
    const cacheData = await this.load(cacheKey);
    
    if (!cacheData || Date.now() > cacheData.expiry) {
      // 缓存过期或不存在
      await this.remove(cacheKey);
      return null;
    }
    
    return cacheData.emails;
  }

  /**
   * 清除过期缓存
   * @returns {Promise<number>} 清除的缓存数量
   */
  async clearExpiredCache() {
    try {
      const allData = await chrome.storage.local.get(null);
      let clearedCount = 0;
      
      for (const [key, value] of Object.entries(allData)) {
        if (key.startsWith('emails_') && value.expiry && Date.now() > value.expiry) {
          await this.remove(key);
          clearedCount++;
        }
      }
      
      console.log(`清除了 ${clearedCount} 个过期缓存`);
      return clearedCount;
    } catch (error) {
      console.error('清除过期缓存失败:', error);
      return 0;
    }
  }

  /**
   * 获取存储使用情况
   * @returns {Promise<Object>} 存储使用情况
   */
  async getStorageUsage() {
    try {
      const localUsage = await chrome.storage.local.getBytesInUse();
      const syncUsage = await chrome.storage.sync.getBytesInUse();
      
      return {
        local: {
          used: localUsage,
          quota: chrome.storage.local.QUOTA_BYTES || 5242880, // 5MB
          percentage: (localUsage / (chrome.storage.local.QUOTA_BYTES || 5242880)) * 100
        },
        sync: {
          used: syncUsage,
          quota: chrome.storage.sync.QUOTA_BYTES || 102400, // 100KB
          percentage: (syncUsage / (chrome.storage.sync.QUOTA_BYTES || 102400)) * 100
        }
      };
    } catch (error) {
      console.error('获取存储使用情况失败:', error);
      return null;
    }
  }

  /**
   * 导出所有数据
   * @returns {Promise<Object>} 所有存储数据
   */
  async exportData() {
    try {
      const localData = await chrome.storage.local.get(null);
      const syncData = await chrome.storage.sync.get(null);
      
      return {
        local: localData,
        sync: syncData,
        exportTime: new Date().toISOString()
      };
    } catch (error) {
      console.error('导出数据失败:', error);
      return null;
    }
  }

  /**
   * 导入数据
   * @param {Object} data 要导入的数据
   * @returns {Promise<boolean>} 是否成功
   */
  async importData(data) {
    try {
      if (data.local) {
        await chrome.storage.local.set(data.local);
      }
      
      if (data.sync) {
        await chrome.storage.sync.set(data.sync);
      }
      
      // 清除内存缓存，强制重新加载
      this.cache.clear();
      this.cacheExpiry.clear();
      
      console.log('数据导入成功');
      return true;
    } catch (error) {
      console.error('导入数据失败:', error);
      return false;
    }
  }
}

// 创建单例实例
export const storageManager = new StorageManager();

// 默认导出
export default storageManager;
