/**
 * API通信模块
 * 负责与后端服务器的所有通信
 */

// 配置常量
const CONFIG = {
  BASE_URL: 'http://1.12.224.176:8080',
  API_KEY: 'mailplugin_api_key_2024',
  TIMEOUT: 10000, // 10秒超时
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000 // 1秒重试延迟
};

/**
 * API客户端类
 */
export class ApiClient {
  constructor() {
    this.isOnline = true;
    this.requestQueue = [];
    this.retryCount = new Map();
  }

  /**
   * 发送HTTP请求的基础方法
   * @param {string} endpoint API端点
   * @param {Object} options 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async request(endpoint, options = {}) {
    const url = `${CONFIG.BASE_URL}${endpoint}`;
    const requestId = `${endpoint}_${Date.now()}`;
    
    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: CONFIG.TIMEOUT,
      ...options
    };

    try {
      console.log(`发送API请求: ${options.method || 'GET'} ${url}`);
      
      // 创建带超时的fetch请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), CONFIG.TIMEOUT);
      
      const response = await fetch(url, {
        ...defaultOptions,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // 重置重试计数
      this.retryCount.delete(requestId);
      this.isOnline = true;
      
      console.log(`API请求成功: ${endpoint}`, data);
      return data;
      
    } catch (error) {
      console.error(`API请求失败: ${endpoint}`, error);
      
      // 处理网络错误
      if (error.name === 'AbortError') {
        throw new Error('请求超时，请检查网络连接');
      }
      
      if (error.message.includes('Failed to fetch')) {
        this.isOnline = false;
        throw new Error('网络连接失败，请检查服务器状态');
      }
      
      throw error;
    }
  }

  /**
   * 带重试机制的请求
   * @param {string} endpoint API端点
   * @param {Object} options 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async requestWithRetry(endpoint, options = {}) {
    const requestId = `${endpoint}_${Date.now()}`;
    let lastError;
    
    for (let attempt = 1; attempt <= CONFIG.MAX_RETRIES; attempt++) {
      try {
        const result = await this.request(endpoint, options);
        return result;
      } catch (error) {
        lastError = error;
        
        if (attempt < CONFIG.MAX_RETRIES) {
          console.log(`请求失败，${CONFIG.RETRY_DELAY}ms后重试 (${attempt}/${CONFIG.MAX_RETRIES})`);
          await this.delay(CONFIG.RETRY_DELAY * attempt);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * 生成临时邮箱
   * @param {Object} emailData 邮箱数据
   * @returns {Promise<Object>} 响应结果
   */
  async generateEmail(emailData) {
    return await this.requestWithRetry('/api/generate-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userId: emailData.userId,
        tempEmail: emailData.tempEmail,
        generateTime: emailData.generateTime,
        apiKey: CONFIG.API_KEY
      })
    });
  }

  /**
   * 获取邮件列表
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 邮件数据
   */
  async getEmails(userId) {
    const params = new URLSearchParams({
      userId: userId,
      apiKey: CONFIG.API_KEY
    });
    
    return await this.requestWithRetry(`/api/get-emails?${params}`);
  }

  /**
   * 获取共享解析规则
   * @returns {Promise<Object>} 规则数据
   */
  async getSharedRules() {
    return await this.requestWithRetry('/api/get-shared-rules');
  }

  /**
   * 上传共享解析规则
   * @param {Object} ruleData 规则数据
   * @returns {Promise<Object>} 响应结果
   */
  async uploadSharedRule(ruleData) {
    return await this.requestWithRetry('/api/upload-shared-rule', {
      method: 'POST',
      body: JSON.stringify({
        apiKey: CONFIG.API_KEY,
        rule: ruleData
      })
    });
  }

  /**
   * 更新解析规则
   * @param {Object} ruleData 规则数据
   * @returns {Promise<Object>} 响应结果
   */
  async updateRule(ruleData) {
    return await this.requestWithRetry('/api/update-rule', {
      method: 'PUT',
      body: JSON.stringify({
        apiKey: CONFIG.API_KEY,
        rule: ruleData
      })
    });
  }

  /**
   * 删除解析规则
   * @param {string} ruleId 规则ID
   * @returns {Promise<Object>} 响应结果
   */
  async deleteRule(ruleId) {
    return await this.requestWithRetry('/api/delete-rule', {
      method: 'DELETE',
      body: JSON.stringify({
        apiKey: CONFIG.API_KEY,
        ruleId: ruleId
      })
    });
  }

  /**
   * 获取规则统计信息
   * @returns {Promise<Object>} 统计数据
   */
  async getRulesStats() {
    return await this.requestWithRetry('/api/rules-stats');
  }

  /**
   * 健康检查
   * @returns {Promise<Object>} 服务器状态
   */
  async healthCheck() {
    try {
      const response = await this.request('/test/health', { timeout: 5000 });
      this.isOnline = true;
      return response;
    } catch (error) {
      this.isOnline = false;
      throw error;
    }
  }

  /**
   * 获取连接状态
   * @returns {boolean} 是否在线
   */
  getConnectionStatus() {
    return this.isOnline;
  }

  /**
   * 测试服务器连接
   * @returns {Promise<Object>} 连接测试结果
   */
  async testConnection() {
    try {
      console.log(`测试服务器连接: ${CONFIG.BASE_URL}`);

      // 尝试多个测试端点
      const testEndpoints = ['/test/health', '/api/get-shared-rules'];

      for (const endpoint of testEndpoints) {
        try {
          const result = await this.request(endpoint, { timeout: 5000 });
          console.log(`连接测试成功: ${endpoint}`, result);
          this.isOnline = true;
          return { success: true, endpoint, result };
        } catch (error) {
          console.log(`连接测试失败: ${endpoint}`, error.message);
        }
      }

      throw new Error('所有测试端点都无法连接');
    } catch (error) {
      console.error('服务器连接测试失败:', error);
      this.isOnline = false;
      return {
        success: false,
        error: error.message,
        suggestions: [
          '1. 检查服务器是否正在运行',
          '2. 检查服务器地址: ' + CONFIG.BASE_URL,
          '3. 检查防火墙设置，确保8080端口开放',
          '4. 检查网络连接是否正常'
        ]
      };
    }
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 批量请求处理
   * @param {Array} requests 请求数组
   * @returns {Promise<Array>} 响应数组
   */
  async batchRequest(requests) {
    const results = [];
    
    for (const request of requests) {
      try {
        const result = await this.requestWithRetry(request.endpoint, request.options);
        results.push({ success: true, data: result });
      } catch (error) {
        results.push({ success: false, error: error.message });
      }
    }
    
    return results;
  }

  /**
   * 设置API配置
   * @param {Object} config 配置对象
   */
  setConfig(config) {
    Object.assign(CONFIG, config);
  }

  /**
   * 获取当前配置
   * @returns {Object} 配置对象
   */
  getConfig() {
    return { ...CONFIG };
  }
}

// 创建单例实例
export const apiClient = new ApiClient();

// 默认导出
export default apiClient;
