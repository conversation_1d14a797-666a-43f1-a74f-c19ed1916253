/**
 * 解析规则管理模块
 * 负责创建、管理和应用邮件解析规则
 */

import { storageManager } from './storage-manager.js';
import { apiClient } from './api-client.js';

/**
 * 解析规则管理器类
 */
export class RuleManager {
  constructor() {
    this.localRules = [];
    this.sharedRules = [];
    this.ruleCache = new Map();
  }

  /**
   * 初始化规则管理器
   */
  async initialize() {
    try {
      await this.loadLocalRules();
      await this.loadSharedRules();
      console.log('规则管理器初始化完成');
    } catch (error) {
      console.error('规则管理器初始化失败:', error);
    }
  }

  /**
   * 从邮件示例创建解析规则
   * @param {Object} ruleData 规则数据
   * @returns {Object} 创建的规则
   */
  createRuleFromExample(ruleData) {
    const { senderEmail, platform, emailExample, codeExample } = ruleData;
    
    if (!senderEmail || !platform || !emailExample || !codeExample) {
      throw new Error('缺少必要的规则参数');
    }

    // 分析验证码模式
    const pattern = this.analyzeCodePattern(emailExample, codeExample);
    
    if (!pattern) {
      throw new Error('无法从示例中提取有效的验证码模式');
    }

    const rule = {
      id: this.generateRuleId(),
      senderEmail: senderEmail.toLowerCase().trim(),
      platform: platform.trim(),
      pattern: pattern.regex,
      patternType: pattern.type,
      example: codeExample.trim(),
      description: `${platform} 验证码提取规则`,
      createdAt: new Date().toISOString(),
      isActive: true,
      usage: 0
    };

    return rule;
  }

  /**
   * 分析验证码模式
   * @param {string} emailContent 邮件内容
   * @param {string} codeExample 验证码示例
   * @returns {Object|null} 模式信息
   */
  analyzeCodePattern(emailContent, codeExample) {
    const code = codeExample.trim();
    
    // 检查验证码类型
    const isNumeric = /^\d+$/.test(code);
    const isAlphaNumeric = /^[a-zA-Z0-9]+$/.test(code);
    const hasSpecialChars = /[^a-zA-Z0-9]/.test(code);
    
    let patterns = [];
    
    if (isNumeric) {
      // 纯数字验证码
      patterns.push({
        regex: `\\b\\d{${code.length}}\\b`,
        type: 'NUMERIC_CODE',
        confidence: 0.9
      });
      
      // 通用数字模式
      if (code.length >= 4 && code.length <= 8) {
        patterns.push({
          regex: '\\b\\d{4,8}\\b',
          type: 'NUMERIC_CODE',
          confidence: 0.7
        });
      }
    } else if (isAlphaNumeric) {
      // 字母数字混合
      patterns.push({
        regex: `\\b[a-zA-Z0-9]{${code.length}}\\b`,
        type: 'ALPHANUMERIC_CODE',
        confidence: 0.8
      });
      
      // 通用字母数字模式
      if (code.length >= 4 && code.length <= 12) {
        patterns.push({
          regex: '\\b[a-zA-Z0-9]{4,12}\\b',
          type: 'ALPHANUMERIC_CODE',
          confidence: 0.6
        });
      }
    } else if (hasSpecialChars) {
      // 包含特殊字符，需要转义
      const escapedCode = code.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      patterns.push({
        regex: escapedCode,
        type: 'SPECIAL_CODE',
        confidence: 0.9
      });
    }

    // 尝试在邮件内容中查找模式
    for (const pattern of patterns) {
      try {
        const regex = new RegExp(pattern.regex, 'gi');
        const matches = emailContent.match(regex);
        
        if (matches && matches.includes(code)) {
          return pattern;
        }
      } catch (error) {
        console.warn('正则表达式测试失败:', pattern.regex, error);
      }
    }

    // 如果没有找到匹配，返回最高置信度的模式
    return patterns.length > 0 ? patterns[0] : null;
  }

  /**
   * 保存规则到本地
   * @param {Object} rule 规则对象
   * @returns {Promise<boolean>} 是否成功
   */
  async saveRuleLocally(rule) {
    try {
      // 检查是否已存在相同发件人的规则
      const existingIndex = this.localRules.findIndex(r => r.senderEmail === rule.senderEmail);
      
      if (existingIndex >= 0) {
        // 更新现有规则
        this.localRules[existingIndex] = { ...rule, updatedAt: new Date().toISOString() };
      } else {
        // 添加新规则
        this.localRules.push(rule);
      }
      
      await storageManager.saveParseRules(this.localRules);
      this.updateRuleCache();
      
      console.log('规则已保存到本地:', rule);
      return true;
    } catch (error) {
      console.error('保存本地规则失败:', error);
      return false;
    }
  }

  /**
   * 上传规则到云端共享
   * @param {Object} rule 规则对象
   * @returns {Promise<boolean>} 是否成功
   */
  async shareRule(rule) {
    try {
      const response = await apiClient.uploadSharedRule(rule);
      
      if (response.success) {
        // 同时保存到本地
        await this.saveRuleLocally(rule);
        
        // 重新加载共享规则
        await this.loadSharedRules();
        
        console.log('规则已共享:', rule);
        return true;
      } else {
        throw new Error(response.message || '上传规则失败');
      }
    } catch (error) {
      console.error('共享规则失败:', error);
      return false;
    }
  }

  /**
   * 加载本地规则
   */
  async loadLocalRules() {
    try {
      this.localRules = await storageManager.loadParseRules();
      this.updateRuleCache();
      console.log(`加载了 ${this.localRules.length} 个本地规则`);
    } catch (error) {
      console.error('加载本地规则失败:', error);
      this.localRules = [];
    }
  }

  /**
   * 加载共享规则
   */
  async loadSharedRules() {
    try {
      const response = await apiClient.getSharedRules();
      
      if (response.success) {
        this.sharedRules = response.rules || [];
        this.updateRuleCache();
        console.log(`加载了 ${this.sharedRules.length} 个共享规则`);
      }
    } catch (error) {
      console.error('加载共享规则失败:', error);
      this.sharedRules = [];
    }
  }

  /**
   * 根据发件人查找规则
   * @param {string} senderEmail 发件人邮箱
   * @returns {Object|null} 匹配的规则
   */
  findRuleForSender(senderEmail) {
    if (!senderEmail) return null;
    
    const normalizedSender = senderEmail.toLowerCase().trim();
    
    // 先查找缓存
    if (this.ruleCache.has(normalizedSender)) {
      return this.ruleCache.get(normalizedSender);
    }
    
    // 优先使用本地规则
    let rule = this.localRules.find(r => r.senderEmail === normalizedSender && r.isActive);
    
    // 如果本地没有，使用共享规则
    if (!rule) {
      rule = this.sharedRules.find(r => r.senderEmail === normalizedSender && r.isActive);
    }
    
    // 缓存结果
    if (rule) {
      this.ruleCache.set(normalizedSender, rule);
    }
    
    return rule || null;
  }

  /**
   * 获取所有可用规则
   * @returns {Array} 规则数组
   */
  getAllRules() {
    return [...this.localRules, ...this.sharedRules];
  }

  /**
   * 删除本地规则
   * @param {string} ruleId 规则ID
   * @returns {Promise<boolean>} 是否成功
   */
  async deleteLocalRule(ruleId) {
    try {
      this.localRules = this.localRules.filter(rule => rule.id !== ruleId);
      await storageManager.saveParseRules(this.localRules);
      this.updateRuleCache();
      
      console.log('本地规则已删除:', ruleId);
      return true;
    } catch (error) {
      console.error('删除本地规则失败:', error);
      return false;
    }
  }

  /**
   * 更新规则使用统计
   * @param {string} ruleId 规则ID
   */
  async updateRuleUsage(ruleId) {
    try {
      const rule = this.localRules.find(r => r.id === ruleId);
      if (rule) {
        rule.usage = (rule.usage || 0) + 1;
        rule.lastUsed = new Date().toISOString();
        await storageManager.saveParseRules(this.localRules);
      }
    } catch (error) {
      console.error('更新规则使用统计失败:', error);
    }
  }

  /**
   * 更新规则缓存
   */
  updateRuleCache() {
    this.ruleCache.clear();
    
    // 缓存所有规则
    [...this.localRules, ...this.sharedRules].forEach(rule => {
      if (rule.isActive) {
        this.ruleCache.set(rule.senderEmail, rule);
      }
    });
  }

  /**
   * 生成规则ID
   * @returns {string} 唯一ID
   */
  generateRuleId() {
    return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 验证规则有效性
   * @param {Object} rule 规则对象
   * @returns {boolean} 是否有效
   */
  validateRule(rule) {
    if (!rule || typeof rule !== 'object') {
      return false;
    }
    
    const required = ['senderEmail', 'platform', 'pattern', 'patternType'];
    return required.every(field => rule[field] && typeof rule[field] === 'string');
  }

  /**
   * 导出规则
   * @returns {Object} 导出数据
   */
  exportRules() {
    return {
      localRules: this.localRules,
      exportTime: new Date().toISOString(),
      version: '1.0'
    };
  }

  /**
   * 导入规则
   * @param {Object} data 导入数据
   * @returns {Promise<boolean>} 是否成功
   */
  async importRules(data) {
    try {
      if (!data.localRules || !Array.isArray(data.localRules)) {
        throw new Error('无效的导入数据格式');
      }

      // 验证所有规则
      const validRules = data.localRules.filter(rule => this.validateRule(rule));

      if (validRules.length === 0) {
        throw new Error('没有找到有效的规则');
      }

      // 合并规则（避免重复）
      const existingSenders = new Set(this.localRules.map(r => r.senderEmail));
      const newRules = validRules.filter(rule => !existingSenders.has(rule.senderEmail));

      this.localRules.push(...newRules);
      await storageManager.saveParseRules(this.localRules);
      this.updateRuleCache();

      console.log(`导入了 ${newRules.length} 个新规则`);
      return true;
    } catch (error) {
      console.error('导入规则失败:', error);
      return false;
    }
  }

  /**
   * 提取邮箱域名
   * @param {string} email 邮箱地址
   * @returns {string} 域名
   */
  extractDomain(email) {
    if (!email || typeof email !== 'string') {
      return '';
    }

    const atIndex = email.lastIndexOf('@');
    if (atIndex === -1) {
      return '';
    }

    return email.substring(atIndex + 1).toLowerCase().trim();
  }

  /**
   * 标准化邮箱地址
   * @param {string} email 邮箱地址
   * @returns {string} 标准化后的邮箱地址
   */
  normalizeEmailAddress(email) {
    if (!email || typeof email !== 'string') {
      return '';
    }

    // 移除多余的空白字符和引号
    let normalized = email.trim().toLowerCase();

    // 移除可能的显示名称 (如 "GitHub <<EMAIL>>" -> "<EMAIL>")
    const emailMatch = normalized.match(/<([^>]+)>/);
    if (emailMatch) {
      normalized = emailMatch[1];
    }

    // 移除可能的前后引号
    normalized = normalized.replace(/^["']|["']$/g, '');

    return normalized;
  }

  /**
   * 计算邮箱匹配置信度
   * @param {string} senderEmail 发件人邮箱
   * @param {Object} rule 规则对象
   * @returns {Object} 匹配结果 {isMatch: boolean, confidence: number, matchType: string}
   */
  calculateMatchConfidence(senderEmail, rule) {
    if (!senderEmail || !rule || !rule.senderEmail) {
      return { isMatch: false, confidence: 0, matchType: 'none' };
    }

    const normalizedSender = this.normalizeEmailAddress(senderEmail);
    const normalizedRule = this.normalizeEmailAddress(rule.senderEmail);

    // 1. 精确匹配 - 最高置信度
    if (normalizedSender === normalizedRule) {
      return { isMatch: true, confidence: 1.0, matchType: 'exact' };
    }

    // 2. 域名匹配
    const senderDomain = this.extractDomain(normalizedSender);
    const ruleDomain = this.extractDomain(normalizedRule);

    if (senderDomain && ruleDomain && senderDomain === ruleDomain) {
      return { isMatch: true, confidence: 0.8, matchType: 'domain' };
    }

    // 3. 子域名匹配
    if (senderDomain && ruleDomain) {
      if (senderDomain.endsWith('.' + ruleDomain) || ruleDomain.endsWith('.' + senderDomain)) {
        return { isMatch: true, confidence: 0.7, matchType: 'subdomain' };
      }
    }

    // 4. 包含匹配 - 较低置信度
    if (normalizedSender.includes(normalizedRule) || normalizedRule.includes(normalizedSender)) {
      return { isMatch: true, confidence: 0.5, matchType: 'contains' };
    }

    // 5. 关键词匹配 - 最低置信度
    const senderParts = normalizedSender.split('@')[0].split(/[._-]/);
    const ruleParts = normalizedRule.split('@')[0].split(/[._-]/);

    const commonParts = senderParts.filter(part =>
      part.length > 2 && ruleParts.some(rulePart =>
        rulePart.includes(part) || part.includes(rulePart)
      )
    );

    if (commonParts.length > 0) {
      return { isMatch: true, confidence: 0.3, matchType: 'keyword' };
    }

    return { isMatch: false, confidence: 0, matchType: 'none' };
  }

  /**
   * 获取规则统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    const totalLocal = this.localRules.length;
    const activeLocal = this.localRules.filter(r => r.isActive).length;
    const totalShared = this.sharedRules.length;
    const activeShared = this.sharedRules.filter(r => r.isActive).length;
    
    return {
      local: {
        total: totalLocal,
        active: activeLocal,
        inactive: totalLocal - activeLocal
      },
      shared: {
        total: totalShared,
        active: activeShared,
        inactive: totalShared - activeShared
      },
      cache: {
        size: this.ruleCache.size
      }
    };
  }
}

// 创建单例实例
export const ruleManager = new RuleManager();

// 默认导出
export default ruleManager;
