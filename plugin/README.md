# 临时邮箱助手 - Edge浏览器插件

## 项目简介

临时邮箱助手是一个Edge浏览器侧边栏插件，为用户提供一次性临时邮箱服务，方便在注册第三方平台时接收验证码。该插件与云服务器后端配合工作，实现邮件的实时接收和验证码自动提取。

## 主要功能

### 🚀 核心功能
- **临时邮箱生成**: 生成8-12位随机字符串的临时邮箱地址（@chaiyuangungunlai.dpdns.org）
- **实时邮件接收**: 5秒内快速获取发送到临时邮箱的邮件
- **验证码自动提取**: 智能识别并提取邮件中的验证码和链接
- **解析规则管理**: 支持创建、保存和共享验证码解析规则

### 📧 邮件处理
- **智能过滤**: 只显示临时邮箱生成后收到的邮件
- **多格式支持**: 支持纯文本、HTML等多种邮件格式
- **实时刷新**: 自动轮询新邮件，无需手动刷新
- **邮件详情**: 完整显示邮件内容和发件人信息

### 🔧 解析规则
- **本地规则**: 保存在浏览器本地，仅当前用户使用
- **共享规则**: 上传到云端，所有用户共享使用
- **智能匹配**: 根据发件人自动应用对应的解析规则
- **规则创建**: 通过邮件示例和验证码示例自动生成解析模式

### 🎨 用户界面
- **侧边栏设计**: 原生Edge侧边栏体验，不干扰正常浏览
- **响应式布局**: 适配不同屏幕尺寸和侧边栏宽度
- **实时状态**: 显示连接状态、邮件数量等实时信息
- **一键操作**: 快速复制邮箱地址、验证码和链接

## 安装方法

### 开发者模式安装

1. **打开Edge浏览器**，进入扩展管理页面：
   ```
   edge://extensions/
   ```

2. **启用开发者模式**：
   - 在页面右上角找到"开发人员模式"开关
   - 将其切换到"开启"状态

3. **加载插件**：
   - 点击"加载解压缩的扩展"按钮
   - 选择插件的`plugin`文件夹
   - 确认加载

4. **验证安装**：
   - 在扩展列表中找到"临时邮箱助手"
   - 确保插件状态为"已启用"
   - 点击浏览器工具栏中的插件图标

### 使用侧边栏

1. **打开侧边栏**：
   - 点击浏览器工具栏中的插件图标
   - 或者右键点击插件图标，选择"在侧边栏中打开"

2. **固定侧边栏**（推荐）：
   - 在侧边栏顶部点击"固定"按钮
   - 这样侧边栏会保持打开状态

## 使用指南

### 生成临时邮箱

1. **点击"生成新邮箱"按钮**
2. **系统自动生成**8-12位随机字符串的邮箱地址
3. **复制邮箱地址**，用于第三方平台注册
4. **等待邮件**，插件会自动开始监听新邮件

### 接收和查看邮件

1. **自动刷新**：插件每5秒自动检查新邮件
2. **邮件列表**：显示所有收到的邮件，包括发件人、主题和时间
3. **邮件详情**：点击邮件项查看完整内容
4. **验证码提取**：自动提取的验证码会显示在专门区域

### 管理解析规则

#### 创建新规则

1. **切换到"创建规则"标签**
2. **填写表单**：
   - 发件人邮箱：例如 `<EMAIL>`
   - 平台名称：例如 `GitHub`
   - 邮件示例：粘贴完整的邮件内容
   - 验证码示例：例如 `123456`
3. **选择保存方式**：
   - "保存到本地"：仅当前浏览器使用
   - "共享给所有用户"：上传到云端供所有用户使用

#### 查看规则

- **本地规则**：查看保存在本地的解析规则
- **共享规则**：查看其他用户分享的解析规则

## 技术架构

### 前端技术
- **Manifest V3**: 使用最新的Chrome扩展标准
- **Sidepanel API**: 原生侧边栏支持
- **ES6+ JavaScript**: 模块化代码结构
- **CSS Grid/Flexbox**: 响应式布局设计

### 模块结构
```
plugin/
├── manifest.json              # 插件配置
├── background/
│   └── service-worker.js      # 后台服务
├── sidepanel/
│   ├── sidepanel.html         # 侧边栏页面
│   ├── sidepanel.css          # 样式文件
│   └── sidepanel.js           # 主要逻辑
├── modules/
│   ├── email-generator.js     # 邮箱生成
│   ├── api-client.js          # API通信
│   ├── storage-manager.js     # 存储管理
│   ├── rule-manager.js        # 规则管理
│   └── email-parser.js        # 邮件解析
└── assets/
    ├── styles/common.css      # 通用样式
    └── images/loading.css     # 加载动画
```

### 后端集成
- **服务器地址**: `https://************:8080`
- **API密钥**: `mailplugin_api_key_2024`
- **主要接口**:
  - `POST /api/generate-email` - 生成临时邮箱
  - `GET /api/get-emails` - 获取邮件
  - `GET /api/get-shared-rules` - 获取共享规则
  - `POST /api/upload-shared-rule` - 上传共享规则

## 常见问题

### Q: 为什么收不到邮件？
A: 请检查：
1. 网络连接是否正常
2. 临时邮箱地址是否正确复制
3. 发件方是否已发送邮件
4. 等待时间（通常5秒内到达）

### Q: 验证码提取不准确怎么办？
A: 可以：
1. 创建针对该平台的自定义解析规则
2. 查看邮件原文，手动复制验证码
3. 分享解析规则帮助其他用户

### Q: 插件无法连接服务器？
A: 请检查：
1. 网络连接状态
2. 服务器是否正常运行
3. 防火墙设置是否阻止连接

### Q: 如何删除本地规则？
A: 目前版本暂不支持删除功能，后续版本会添加此功能。

## 隐私说明

- **无用户数据持久化**: 除解析规则外，不存储任何用户数据
- **临时邮箱**: 24小时后自动失效
- **本地存储**: 解析规则可选择仅保存在本地
- **安全传输**: 所有数据传输使用HTTPS加密

## 更新日志

### v1.0.0 (2024-08-04)
- 🎉 首次发布
- ✨ 支持临时邮箱生成和邮件接收
- ✨ 智能验证码提取功能
- ✨ 解析规则管理系统
- ✨ 侧边栏用户界面
- ✨ 实时邮件轮询

## 技术支持

如有问题或建议，请联系开发团队。

---

**注意**: 本插件仅用于合法的邮件接收用途，请勿用于任何违法违规活动。
