# 临时邮箱助手 - 安装指南

## 系统要求

### 浏览器要求
- **Microsoft Edge**: 版本 88 或更高
- **Google Chrome**: 版本 88 或更高（如需兼容）
- **支持Manifest V3**: 必须支持最新的扩展标准

### 系统要求
- **操作系统**: Windows 10/11, macOS 10.14+, Linux
- **网络连接**: 需要访问互联网连接后端服务器
- **存储空间**: 约 5MB 可用空间

## 安装步骤

### 方法一：开发者模式安装（推荐）

#### 1. 准备插件文件
确保您已获得完整的插件文件夹，包含以下结构：
```
plugin/
├── manifest.json
├── background/
├── sidepanel/
├── modules/
├── assets/
└── icons/
```

#### 2. 打开Edge浏览器扩展管理
1. 启动 Microsoft Edge 浏览器
2. 在地址栏输入：`edge://extensions/`
3. 按回车键进入扩展管理页面

#### 3. 启用开发者模式
1. 在扩展管理页面的左下角找到"开发人员模式"
2. 将开关切换到"开启"状态
3. 页面会刷新并显示开发者选项

#### 4. 加载插件
1. 点击"加载解压缩的扩展"按钮
2. 在文件浏览器中导航到插件的 `plugin` 文件夹
3. 选择该文件夹并点击"选择文件夹"
4. 插件将被加载并出现在扩展列表中

#### 5. 验证安装
1. 检查插件是否出现在扩展列表中
2. 确认插件状态为"已启用"
3. 在浏览器工具栏中应该能看到插件图标

### 方法二：打包安装

#### 1. 打包插件（可选）
如果您需要分发插件，可以将其打包：
1. 在扩展管理页面点击"打包扩展程序"
2. 选择插件的 `plugin` 文件夹
3. 生成 `.crx` 文件和 `.pem` 密钥文件

#### 2. 安装打包文件
1. 将 `.crx` 文件拖拽到扩展管理页面
2. 点击"添加扩展程序"确认安装

## 配置设置

### 1. 侧边栏设置
1. 右键点击插件图标
2. 选择"在侧边栏中打开"
3. 或者点击插件图标直接打开侧边栏

### 2. 固定侧边栏（推荐）
1. 打开侧边栏后，点击顶部的"固定"按钮
2. 这样侧边栏会保持打开状态，方便使用

### 3. 权限确认
插件需要以下权限：
- **存储权限**: 保存用户设置和解析规则
- **网络权限**: 连接后端服务器获取邮件
- **侧边栏权限**: 在侧边栏中显示界面

## 功能测试

### 1. 基础功能测试
1. 打开插件侧边栏
2. 点击"生成新邮箱"按钮
3. 检查是否成功生成临时邮箱地址
4. 观察状态指示器是否显示"就绪"

### 2. 网络连接测试
1. 在插件目录中打开 `test.html` 文件
2. 运行所有测试检查各项功能
3. 确认API连接状态为"连接成功"

### 3. 邮件接收测试
1. 使用生成的临时邮箱注册任意服务
2. 等待验证邮件发送
3. 检查插件是否能接收并显示邮件
4. 验证验证码是否被正确提取

## 故障排除

### 常见问题及解决方案

#### 问题1: 插件无法加载
**症状**: 加载插件时出现错误
**解决方案**:
1. 检查文件夹结构是否完整
2. 确认 `manifest.json` 文件格式正确
3. 检查是否启用了开发者模式
4. 尝试重新加载插件

#### 问题2: 侧边栏无法打开
**症状**: 点击插件图标无反应
**解决方案**:
1. 检查浏览器是否支持Sidepanel API
2. 更新浏览器到最新版本
3. 重启浏览器后重试
4. 检查插件是否已启用

#### 问题3: 无法连接服务器
**症状**: 状态显示"连接失败"
**解决方案**:
1. 检查网络连接是否正常
2. 确认服务器地址配置正确
3. 检查防火墙设置
4. 尝试使用测试页面检查连接

#### 问题4: 收不到邮件
**症状**: 生成邮箱后无法接收邮件
**解决方案**:
1. 确认邮箱地址复制正确
2. 检查发件方是否已发送
3. 等待更长时间（最多5分钟）
4. 手动点击"刷新邮件"按钮

#### 问题5: 验证码提取不准确
**症状**: 无法正确提取验证码
**解决方案**:
1. 查看邮件原文内容
2. 创建自定义解析规则
3. 手动复制验证码
4. 分享解析规则帮助改进

### 日志查看

#### 1. 浏览器控制台
1. 按 `F12` 打开开发者工具
2. 切换到"控制台"标签
3. 查看插件相关的错误信息

#### 2. 扩展程序页面
1. 在扩展管理页面找到插件
2. 点击"详细信息"
3. 点击"检查视图"查看后台页面日志

#### 3. 服务工作进程
1. 在扩展详情页面
2. 找到"服务工作进程"部分
3. 点击"检查"查看后台日志

## 卸载插件

### 完全卸载步骤
1. 打开扩展管理页面 (`edge://extensions/`)
2. 找到"临时邮箱助手"插件
3. 点击"移除"按钮
4. 确认删除操作

### 清理数据（可选）
1. 打开浏览器设置
2. 进入"隐私、搜索和服务"
3. 点击"清除浏览数据"
4. 选择"扩展程序数据"并清除

## 更新插件

### 开发版本更新
1. 下载新版本的插件文件
2. 在扩展管理页面点击"重新加载"按钮
3. 或者移除旧版本后重新加载新版本

### 自动更新（未来版本）
当插件发布到官方商店后，将支持自动更新功能。

## 技术支持

### 获取帮助
- 查看 `README.md` 文件了解详细功能说明
- 使用 `test.html` 页面进行功能测试
- 检查浏览器控制台的错误信息

### 反馈问题
如遇到问题，请提供以下信息：
1. 浏览器版本和操作系统
2. 插件版本号
3. 具体的错误信息或截图
4. 重现问题的步骤

---

**注意**: 本插件目前为开发版本，建议在测试环境中使用。正式使用前请确保所有功能正常工作。
