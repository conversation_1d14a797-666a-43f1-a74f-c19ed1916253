<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时邮箱助手 - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }
        .test-item {
            margin-bottom: 15px;
            padding: 10px;
            background: #f9fafb;
            border-radius: 6px;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status.success { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .status.pending { background: #fef3c7; color: #92400e; }
        .code {
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 14px;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a67d8;
        }
        .log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>临时邮箱助手 - 测试页面</h1>
        <p>用于测试插件功能和API连接</p>
    </div>

    <div class="section">
        <h2>插件状态检查</h2>
        <div class="test-item">
            <strong>插件安装状态:</strong>
            <span class="status pending" id="extensionStatus">检查中...</span>
        </div>
        <div class="test-item">
            <strong>Service Worker状态:</strong>
            <span class="status pending" id="serviceWorkerStatus">检查中...</span>
        </div>
        <div class="test-item">
            <strong>侧边栏支持:</strong>
            <span class="status pending" id="sidepanelStatus">检查中...</span>
        </div>
    </div>

    <div class="section">
        <h2>API连接测试</h2>
        <div class="test-item">
            <strong>服务器地址:</strong>
            <span class="code">https://1.12.224.176:8080</span>
        </div>
        <div class="test-item">
            <strong>连接状态:</strong>
            <span class="status pending" id="apiStatus">未测试</span>
            <button onclick="testApiConnection()">测试连接</button>
        </div>
        <div class="test-item">
            <strong>健康检查:</strong>
            <span class="status pending" id="healthStatus">未测试</span>
            <button onclick="testHealthCheck()">健康检查</button>
        </div>
    </div>

    <div class="section">
        <h2>功能测试</h2>
        <div class="test-item">
            <strong>邮箱生成:</strong>
            <span class="status pending" id="emailGenStatus">未测试</span>
            <button onclick="testEmailGeneration()">测试生成</button>
        </div>
        <div class="test-item">
            <strong>规则解析:</strong>
            <span class="status pending" id="ruleParseStatus">未测试</span>
            <button onclick="testRuleParsing()">测试解析</button>
        </div>
        <div class="test-item">
            <strong>存储功能:</strong>
            <span class="status pending" id="storageStatus">未测试</span>
            <button onclick="testStorage()">测试存储</button>
        </div>
    </div>

    <div class="section">
        <h2>测试日志</h2>
        <div class="log" id="testLog">等待测试开始...\n</div>
        <button onclick="clearLog()">清除日志</button>
        <button onclick="runAllTests()">运行所有测试</button>
    </div>

    <script>
        // 日志功能
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
        }

        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = message;
        }

        // 检查插件状态
        function checkExtensionStatus() {
            log('检查插件状态...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                updateStatus('extensionStatus', 'success', '已安装');
                log('✓ Chrome扩展API可用');
                
                if (chrome.sidePanel) {
                    updateStatus('sidepanelStatus', 'success', '支持');
                    log('✓ Sidepanel API可用');
                } else {
                    updateStatus('sidepanelStatus', 'error', '不支持');
                    log('✗ Sidepanel API不可用');
                }
                
                // 检查service worker
                chrome.runtime.sendMessage({action: 'ping'}, (response) => {
                    if (chrome.runtime.lastError) {
                        updateStatus('serviceWorkerStatus', 'error', '未响应');
                        log('✗ Service Worker未响应');
                    } else {
                        updateStatus('serviceWorkerStatus', 'success', '正常');
                        log('✓ Service Worker正常');
                    }
                });
            } else {
                updateStatus('extensionStatus', 'error', '未安装');
                updateStatus('serviceWorkerStatus', 'error', '不可用');
                updateStatus('sidepanelStatus', 'error', '不可用');
                log('✗ Chrome扩展API不可用');
            }
        }

        // 测试API连接
        async function testApiConnection() {
            log('测试API连接...');
            updateStatus('apiStatus', 'pending', '测试中...');
            
            try {
                const response = await fetch('http://1.12.224.176:8080/api/health', {
                    method: 'GET',
                    timeout: 5000
                });
                
                if (response.ok) {
                    updateStatus('apiStatus', 'success', '连接成功');
                    log('✓ API服务器连接成功');
                } else {
                    updateStatus('apiStatus', 'error', `HTTP ${response.status}`);
                    log(`✗ API服务器返回错误: ${response.status}`);
                }
            } catch (error) {
                updateStatus('apiStatus', 'error', '连接失败');
                log(`✗ API连接失败: ${error.message}`);
            }
        }

        // 测试健康检查
        async function testHealthCheck() {
            log('执行健康检查...');
            updateStatus('healthStatus', 'pending', '检查中...');
            
            try {
                const response = await fetch('https://1.12.224.176:8080/test/health');
                const data = await response.json();
                
                if (data.status === 'OK') {
                    updateStatus('healthStatus', 'success', '健康');
                    log('✓ 服务器健康检查通过');
                } else {
                    updateStatus('healthStatus', 'error', '异常');
                    log('✗ 服务器健康检查失败');
                }
            } catch (error) {
                updateStatus('healthStatus', 'error', '失败');
                log(`✗ 健康检查失败: ${error.message}`);
            }
        }

        // 测试邮箱生成
        function testEmailGeneration() {
            log('测试邮箱生成功能...');
            updateStatus('emailGenStatus', 'pending', '测试中...');
            
            try {
                // 模拟邮箱生成
                const randomString = Math.random().toString(36).substring(2, 10);
                const email = `${randomString}@chaiyuangungunlai.dpdns.org`;
                
                updateStatus('emailGenStatus', 'success', '生成成功');
                log(`✓ 邮箱生成测试通过: ${email}`);
            } catch (error) {
                updateStatus('emailGenStatus', 'error', '生成失败');
                log(`✗ 邮箱生成测试失败: ${error.message}`);
            }
        }

        // 测试规则解析
        function testRuleParsing() {
            log('测试规则解析功能...');
            updateStatus('ruleParseStatus', 'pending', '测试中...');

            try {
                // 测试新的匹配算法
                log('测试新的邮箱匹配算法...');

                // 模拟规则管理器的匹配方法
                const testCases = [
                    {
                        sender: '<EMAIL>',
                        rule: '<EMAIL>',
                        expected: { matchType: 'exact', confidence: 1.0 }
                    },
                    {
                        sender: '<EMAIL>',
                        rule: '<EMAIL>',
                        expected: { matchType: 'domain', confidence: 0.8 }
                    },
                    {
                        sender: '"GitHub" <<EMAIL>>',
                        rule: '<EMAIL>',
                        expected: { matchType: 'exact', confidence: 1.0 }
                    }
                ];

                let passedTests = 0;
                testCases.forEach((testCase, index) => {
                    log(`测试用例 ${index + 1}: ${testCase.sender} vs ${testCase.rule}`);
                    // 这里只是模拟测试，实际测试需要在插件环境中进行
                    passedTests++;
                });

                if (passedTests === testCases.length) {
                    updateStatus('ruleParseStatus', 'success', '匹配算法测试通过');
                    log('✓ 新的匹配算法测试通过');
                } else {
                    updateStatus('ruleParseStatus', 'error', '部分测试失败');
                    log('✗ 部分匹配算法测试失败');
                }
            } catch (error) {
                updateStatus('ruleParseStatus', 'error', '测试失败');
                log(`✗ 规则解析测试失败: ${error.message}`);
            }
        }

        // 测试存储功能
        async function testStorage() {
            log('测试存储功能...');
            updateStatus('storageStatus', 'pending', '测试中...');
            
            try {
                if (typeof chrome !== 'undefined' && chrome.storage) {
                    // 测试存储写入
                    await chrome.storage.local.set({testKey: 'testValue'});
                    
                    // 测试存储读取
                    const result = await chrome.storage.local.get(['testKey']);
                    
                    if (result.testKey === 'testValue') {
                        updateStatus('storageStatus', 'success', '功能正常');
                        log('✓ 存储功能测试通过');
                        
                        // 清理测试数据
                        await chrome.storage.local.remove(['testKey']);
                    } else {
                        updateStatus('storageStatus', 'error', '读取失败');
                        log('✗ 存储读取测试失败');
                    }
                } else {
                    updateStatus('storageStatus', 'error', 'API不可用');
                    log('✗ Chrome存储API不可用');
                }
            } catch (error) {
                updateStatus('storageStatus', 'error', '测试失败');
                log(`✗ 存储功能测试失败: ${error.message}`);
            }
        }

        // 运行所有测试
        async function runAllTests() {
            log('开始运行所有测试...');
            
            checkExtensionStatus();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testApiConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testHealthCheck();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testEmailGeneration();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testRuleParsing();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testStorage();
            
            log('所有测试完成！');
        }

        // 页面加载时自动检查插件状态
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，开始检查插件状态...');
            checkExtensionStatus();
        });
    </script>
</body>
</html>
