/**
 * 临时邮箱助手 - Service Worker
 * 负责后台任务管理、API代理和消息传递
 */

// 配置常量
const CONFIG = {
  API_BASE_URL: 'http://1.12.224.176:8080/api',
  API_KEY: 'mailplugin_api_key_2024',
  POLL_INTERVAL: 5000, // 5秒轮询间隔
  MAX_RETRIES: 3
};

// 全局状态管理
let activePolling = new Map(); // 存储活跃的轮询任务
let userSessions = new Map(); // 存储用户会话信息

/**
 * 插件安装时的初始化
 */
chrome.runtime.onInstalled.addListener((details) => {
  console.log('临时邮箱助手已安装', details);
  
  // 设置侧边栏
  chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });
  
  // 初始化存储
  initializeStorage();
});

/**
 * 插件启动时的初始化
 */
chrome.runtime.onStartup.addListener(() => {
  console.log('临时邮箱助手已启动');
  initializeStorage();
});

/**
 * 初始化本地存储
 */
async function initializeStorage() {
  try {
    const result = await chrome.storage.local.get(['userSessions', 'parseRules']);
    
    if (!result.userSessions) {
      await chrome.storage.local.set({ userSessions: {} });
    }
    
    if (!result.parseRules) {
      await chrome.storage.local.set({ parseRules: [] });
    }
    
    console.log('存储初始化完成');
  } catch (error) {
    console.error('存储初始化失败:', error);
  }
}

/**
 * 处理来自侧边栏的消息
 */
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('收到消息:', message);
  
  switch (message.action) {
    case 'generateEmail':
      handleGenerateEmail(message.data, sendResponse);
      break;
      
    case 'getEmails':
      handleGetEmails(message.data, sendResponse);
      break;
      
    case 'startPolling':
      handleStartPolling(message.data, sendResponse);
      break;
      
    case 'stopPolling':
      handleStopPolling(message.data, sendResponse);
      break;
      
    case 'getSharedRules':
      handleGetSharedRules(sendResponse);
      break;
      
    case 'uploadSharedRule':
      handleUploadSharedRule(message.data, sendResponse);
      break;
      
    default:
      sendResponse({ success: false, message: '未知的操作类型' });
  }
  
  return true; // 保持消息通道开放
});

/**
 * 处理生成临时邮箱请求
 */
async function handleGenerateEmail(data, sendResponse) {
  try {
    const response = await fetch(`${CONFIG.API_BASE_URL}/generate-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: data.userId,
        tempEmail: data.tempEmail,
        generateTime: data.generateTime,
        apiKey: CONFIG.API_KEY
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // 保存用户会话信息
      userSessions.set(data.userId, {
        tempEmail: data.tempEmail,
        generateTime: data.generateTime,
        lastCheck: new Date().toISOString(),
        lastNotifiedCount: 0 // 添加邮件通知计数器
      });

      // 保存到本地存储
      await saveUserSession(data.userId, userSessions.get(data.userId));
    }
    
    sendResponse(result);
  } catch (error) {
    console.error('生成邮箱失败:', error);
    sendResponse({ 
      success: false, 
      message: '网络连接失败: ' + error.message 
    });
  }
}

/**
 * 处理获取邮件请求
 */
async function handleGetEmails(data, sendResponse) {
  try {
    const url = `${CONFIG.API_BASE_URL}/get-emails?userId=${data.userId}&apiKey=${CONFIG.API_KEY}`;
    const response = await fetch(url);
    const result = await response.json();
    
    sendResponse(result);
  } catch (error) {
    console.error('获取邮件失败:', error);
    sendResponse({ 
      success: false, 
      message: '获取邮件失败: ' + error.message 
    });
  }
}

/**
 * 开始邮件轮询
 */
function handleStartPolling(data, sendResponse) {
  const { userId } = data;

  if (activePolling.has(userId)) {
    clearInterval(activePolling.get(userId));
  }

  // 确保用户会话存在且包含lastNotifiedCount字段
  if (!userSessions.has(userId)) {
    userSessions.set(userId, {
      lastNotifiedCount: 0,
      lastCheck: new Date().toISOString()
    });
  } else {
    const session = userSessions.get(userId);
    if (session.lastNotifiedCount === undefined) {
      session.lastNotifiedCount = 0;
      userSessions.set(userId, session);
    }
  }
  
  const intervalId = setInterval(async () => {
    try {
      const url = `${CONFIG.API_BASE_URL}/get-emails?userId=${userId}&apiKey=${CONFIG.API_KEY}`;
      const response = await fetch(url);
      const result = await response.json();

      if (result.success) {
        const currentCount = result.emails ? result.emails.length : 0;
        const session = userSessions.get(userId);
        const lastNotifiedCount = session ? session.lastNotifiedCount : 0;

        let isNewEmails = false;
        let newEmailCount = 0;

        // 检查是否有新邮件
        if (currentCount > lastNotifiedCount) {
          isNewEmails = true;
          newEmailCount = currentCount - lastNotifiedCount;

          // 更新会话中的通知计数
          if (session) {
            session.lastNotifiedCount = currentCount;
            session.lastCheck = new Date().toISOString();
            userSessions.set(userId, session);
            await saveUserSession(userId, session);
          }
        }

        // 始终发送邮件数据给前端，确保界面能正常更新
        chrome.runtime.sendMessage({
          action: 'emailsUpdated',
          data: {
            ...result,
            newEmailCount: newEmailCount,
            isNewEmails: isNewEmails
          }
        }).catch(() => {
          // 忽略发送失败（侧边栏可能未打开）
        });
      }
    } catch (error) {
      console.error('轮询邮件失败:', error);
    }
  }, CONFIG.POLL_INTERVAL);
  
  activePolling.set(userId, intervalId);
  sendResponse({ success: true, message: '开始轮询邮件' });
}

/**
 * 停止邮件轮询
 */
function handleStopPolling(data, sendResponse) {
  const { userId } = data;
  
  if (activePolling.has(userId)) {
    clearInterval(activePolling.get(userId));
    activePolling.delete(userId);
    sendResponse({ success: true, message: '停止轮询邮件' });
  } else {
    sendResponse({ success: false, message: '没有活跃的轮询任务' });
  }
}

/**
 * 获取共享解析规则
 */
async function handleGetSharedRules(sendResponse) {
  try {
    const response = await fetch(`${CONFIG.API_BASE_URL}/get-shared-rules`);
    const result = await response.json();
    sendResponse(result);
  } catch (error) {
    console.error('获取共享规则失败:', error);
    sendResponse({ 
      success: false, 
      message: '获取共享规则失败: ' + error.message 
    });
  }
}

/**
 * 上传共享解析规则
 */
async function handleUploadSharedRule(data, sendResponse) {
  try {
    const response = await fetch(`${CONFIG.API_BASE_URL}/upload-shared-rule`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        apiKey: CONFIG.API_KEY,
        rule: data.rule
      })
    });
    
    const result = await response.json();
    sendResponse(result);
  } catch (error) {
    console.error('上传共享规则失败:', error);
    sendResponse({ 
      success: false, 
      message: '上传共享规则失败: ' + error.message 
    });
  }
}

/**
 * 保存用户会话到本地存储
 */
async function saveUserSession(userId, sessionData) {
  try {
    const result = await chrome.storage.local.get(['userSessions']);
    const sessions = result.userSessions || {};
    sessions[userId] = sessionData;
    await chrome.storage.local.set({ userSessions: sessions });
  } catch (error) {
    console.error('保存用户会话失败:', error);
  }
}

/**
 * 清理过期的轮询任务
 */
setInterval(() => {
  const now = Date.now();
  for (const [userId, intervalId] of activePolling.entries()) {
    const session = userSessions.get(userId);
    if (!session || (now - new Date(session.lastCheck).getTime()) > 300000) { // 5分钟无活动
      clearInterval(intervalId);
      activePolling.delete(userId);
      userSessions.delete(userId);
      console.log(`清理过期轮询任务: ${userId}`);
    }
  }
}, 60000); // 每分钟检查一次
